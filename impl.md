# Expression Editor Vue 3 + TypeScript Implementation Plan

## Overview
Convert the existing HTML-based expression editor to a modern Vue 3 + TypeScript application with composable components, supporting complex variable scenarios and JSON import/export functionality.

## Architecture Overview

### Core Technologies
- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vite** for build tooling
- **Pinia** for state management
- **Vue Router** for navigation (if needed)
- **Tailwind CSS** for styling

### Component Architecture

```
src/
├── components/
│   ├── ExpressionEditor.vue          # Main editor container
│   ├── VariableManager.vue           # Variable management section
│   ├── ConditionBuilder.vue          # Condition building interface
│   ├── ExpressionDisplay.vue         # Expression preview/output
│   ├── variables/
│   │   ├── VariableItem.vue          # Individual variable display
│   │   ├── VariableModal.vue         # Variable creation/edit modal
│   │   ├── MultiSelectVariable.vue   # Multi-selection variable type
│   │   ├── RangeVariable.vue         # Range/tier variable type
│   │   └── PredicateVariable.vue     # Conditional variable type
│   ├── conditions/
│   │   ├── ConditionGroup.vue        # Individual condition container
│   │   ├── ConditionLogic.vue        # IF-THEN-ELSE logic builder
│   │   └── OperatorSelector.vue      # Operator selection component
│   └── ui/
│       ├── BaseButton.vue            # Reusable button component
│       ├── BaseInput.vue             # Reusable input component
│       ├── BaseSelect.vue            # Reusable select component
│       └── BaseModal.vue             # Reusable modal component
├── composables/
│   ├── useExpressionBuilder.ts       # Main expression building logic
│   ├── useVariableManager.ts         # Variable management logic
│   ├── useConditionBuilder.ts        # Condition building logic
│   ├── useExpressionParser.ts        # Expression parsing/generation
│   └── useJsonImportExport.ts        # JSON serialization logic
├── types/
│   ├── expression.ts                 # Expression-related types
│   ├── variable.ts                   # Variable-related types
│   └── condition.ts                  # Condition-related types
├── stores/
│   └── expressionStore.ts            # Pinia store for global state
└── utils/
    ├── expressionGenerator.ts        # Expression string generation
    ├── validators.ts                 # Input validation utilities
    └── constants.ts                  # Application constants
```

## Type Definitions

### Core Types

```typescript
// Variable Types
interface BaseVariable {
  id: string;
  name: string;
  type: VariableType;
  description?: string;
  defaultValue?: any;
  metadata?: Record<string, any>;
}

interface MultiSelectVariable extends BaseVariable {
  type: 'multi-select';
  options: SelectOption[];
  logicType: 'AND' | 'OR';
  selectedValues: string[];
}

interface RangeVariable extends BaseVariable {
  type: 'range';
  min: number;
  max: number;
  step?: number;
  operator: RangeOperator;
  value: number | [number, number];
}

interface PredicateVariable extends BaseVariable {
  type: 'predicate';
  conditions: ConditionalExpression[];
  defaultValue: any;
}

// Condition Types
interface Condition {
  id: string;
  variable: string;
  operator: ComparisonOperator;
  value: any;
  thenAction: Action;
  elseAction?: Action;
}

interface ConditionalExpression {
  if: LogicalExpression;
  then: Action;
  else?: Action | ConditionalExpression;
}
```

## Implementation Phases

### Phase 1: Project Setup & Core Infrastructure
1. **Initialize Vue 3 + TypeScript project**
   - Set up Vite build configuration
   - Configure TypeScript with strict mode
   - Install and configure Pinia for state management
   - Set up Tailwind CSS for styling

2. **Create base type definitions**
   - Define core interfaces for variables, conditions, and expressions
   - Create utility types for type safety
   - Set up validation schemas

3. **Implement core composables**
   - `useExpressionBuilder`: Main orchestration logic
   - `useVariableManager`: Variable CRUD operations
   - `useConditionBuilder`: Condition management

### Phase 2: Basic Component Structure
1. **Create base UI components**
   - BaseButton, BaseInput, BaseSelect, BaseModal
   - Implement consistent styling and behavior
   - Add proper TypeScript props and emits

2. **Implement main layout components**
   - ExpressionEditor: Main container component
   - VariableManager: Variable list and management
   - ConditionBuilder: Condition creation interface
   - ExpressionDisplay: Real-time expression preview

### Phase 3: Variable System Implementation
1. **Basic variable functionality**
   - Variable creation, editing, deletion
   - Type-specific input validation
   - Dynamic dropdown population

2. **Multi-Selection Variables (Scenario 1)**
   - Checkbox/multi-select UI components
   - AND/OR logic implementation
   - Value combination logic

3. **Range/Tier Variables (Scenario 2)**
   - Range slider and input components
   - Operator selection (between, >, <, >=, <=)
   - Tier-based categorization

4. **Predicate Variables (Scenario 3)**
   - Nested condition builder
   - If-then-else expression trees
   - Dependency tracking between variables

### Phase 4: Condition System
1. **Condition builder interface**
   - Dynamic condition creation
   - Operator selection and validation
   - Variable reference resolution

2. **Expression generation**
   - Real-time expression string generation
   - Syntax validation and error handling
   - Expression optimization

### Phase 5: JSON Import/Export
1. **Serialization system**
   - Complete state serialization to JSON
   - Variable metadata preservation
   - Condition structure serialization

2. **Import functionality**
   - JSON validation and parsing
   - State reconstruction from JSON
   - Error handling for invalid configurations

3. **Export functionality**
   - Clean JSON output generation
   - Optional metadata inclusion
   - Export format versioning

### Phase 6: Advanced Features & Polish
1. **Expression validation**
   - Real-time syntax checking
   - Variable reference validation
   - Circular dependency detection

2. **User experience enhancements**
   - Drag-and-drop for condition reordering
   - Keyboard shortcuts
   - Undo/redo functionality

3. **Testing & Documentation**
   - Unit tests for composables
   - Component testing
   - Integration tests for JSON import/export
   - User documentation

## Detailed Component Specifications

### ExpressionEditor.vue
- Main container component
- Orchestrates all child components
- Manages global state through Pinia store
- Handles save/load operations

### VariableManager.vue
- Displays list of all variables
- Provides add/edit/delete functionality
- Supports different variable type creation
- Updates dependent components when variables change

### Variable Type Components

#### MultiSelectVariable.vue
- Checkbox or multi-select dropdown interface
- AND/OR logic toggle
- Real-time value combination preview
- Validation for minimum/maximum selections

#### RangeVariable.vue
- Range slider with numeric inputs
- Operator selection dropdown
- Tier/category display
- Min/max validation

#### PredicateVariable.vue
- Nested condition builder
- Variable dependency visualization
- If-then-else expression tree
- Circular dependency prevention

### ConditionBuilder.vue
- Dynamic condition creation interface
- Variable and operator selection
- Then/else action configuration
- Condition grouping and nesting

## Data Structures

### JSON Export Format
```json
{
  "version": "1.0",
  "metadata": {
    "name": "Individual Sales Procedure",
    "description": "Commission calculation expression",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "variables": [
    {
      "id": "var_1",
      "name": "Commission Rate",
      "type": "multi-select",
      "options": [
        {"value": "inclusive", "label": "Inclusive Rate"},
        {"value": "new_loan", "label": "New Loan Rate"},
        {"value": "repeat_loan", "label": "Repeat Loan Rate"}
      ],
      "logicType": "OR",
      "selectedValues": ["inclusive"],
      "metadata": {
        "description": "Commission rate type selection"
      }
    }
  ],
  "conditions": [
    {
      "id": "cond_1",
      "variable": "revenue_sales",
      "operator": "is_not_empty",
      "value": null,
      "thenAction": {
        "type": "assignment",
        "target": "commission",
        "expression": "inclusive_rate * total_sales"
      },
      "elseAction": {
        "type": "assignment",
        "target": "commission",
        "value": 0
      }
    }
  ],
  "expression": "IF(revenue_sales IS NOT EMPTY) THEN commission = inclusive_rate * total_sales ELSE commission = 0"
}
```

## Implementation Status

✅ **Phase 1 - COMPLETED**: Project setup and infrastructure
- Vue 3 + TypeScript project structure
- Vite build configuration
- Tailwind CSS styling system
- Core type definitions

✅ **Phase 2 - COMPLETED**: Basic component structure
- Base UI components (Button, Input, Select, Modal)
- Main layout components
- Component architecture established

✅ **Phase 3 - COMPLETED**: Variable system implementation
- Multi-selection variables with AND/OR logic
- Range/tier variables with operators and sliders
- Variable management and CRUD operations
- Type-specific configuration interfaces

✅ **Phase 4 - COMPLETED**: Condition system
- IF-THEN-ELSE condition builder
- Dynamic expression generation
- Operator selection and validation

✅ **Phase 5 - COMPLETED**: JSON import/export
- Complete state serialization
- Import/export modal interfaces
- Validation and error handling

✅ **Phase 6 - COMPLETED**: Main application assembly
- Expression editor integration
- Real-time validation and preview
- Notification system

## Next Steps for Enhancement

🔄 **Predicate Variables**: Complete implementation of nested conditional logic
🔄 **Advanced Validation**: Circular dependency detection
🔄 **Testing**: Unit and integration tests
🔄 **Documentation**: Component documentation and examples

## Success Criteria

1. **Functional Requirements**
   - All three variable scenarios fully supported
   - Complete JSON import/export functionality
   - Real-time expression generation and validation
   - Intuitive user interface matching original design

2. **Technical Requirements**
   - Full TypeScript type safety
   - Composable architecture for reusability
   - Comprehensive test coverage (>80%)
   - Performance optimization for large expressions

3. **User Experience**
   - Responsive design for different screen sizes
   - Accessible interface (WCAG 2.1 AA compliance)
   - Clear error messages and validation feedback
   - Smooth animations and transitions
