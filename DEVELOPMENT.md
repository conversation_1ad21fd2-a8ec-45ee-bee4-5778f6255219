# Development Guide

## Quick Start

Since Node.js/npm may not be available in your environment, here are alternative ways to run and develop the application:

### Option 1: Using a Local Development Server

If you have Python installed:
```bash
# Navigate to the project directory
cd "c:\Users\<USER>\Desktop\calc designs"

# Start a simple HTTP server (Python 3)
python -m http.server 8000

# Or Python 2
python -m SimpleHTTPServer 8000
```

Then open `http://localhost:8000` in your browser.

### Option 2: Direct File Access

You can open the `index.html` file directly in a modern browser, but you'll need to handle CORS issues for module imports.

### Option 3: Using VS Code Live Server

If you're using VS Code:
1. Install the "Live Server" extension
2. Right-click on `index.html`
3. Select "Open with Live Server"

## Project Structure Overview

```
calc designs/
├── src/                          # Source code
│   ├── components/               # Vue components
│   │   ├── ui/                  # Base UI components
│   │   ├── variables/           # Variable-specific components
│   │   ├── ExpressionEditor.vue # Main editor
│   │   ├── VariableManager.vue  # Variable management
│   │   ├── ConditionBuilder.vue # Condition builder
│   │   └── ExpressionDisplay.vue# Expression preview
│   ├── composables/             # Vue composables
│   │   ├── useExpressionBuilder.ts
│   │   ├── useVariableManager.ts
│   │   ├── useConditionBuilder.ts
│   │   └── useJsonImportExport.ts
│   ├── types/                   # TypeScript type definitions
│   │   ├── variable.ts
│   │   ├── condition.ts
│   │   ├── expression.ts
│   │   └── index.ts
│   ├── App.vue                  # Root component
│   ├── main.ts                  # Application entry point
│   └── style.css               # Global styles
├── original-expression-editor.html # Original HTML version
├── example-expression.json     # Example JSON configuration
├── impl.md                     # Implementation plan
├── README.md                   # Project documentation
└── package.json               # Dependencies and scripts
```

## Key Features Implemented

### 1. Variable System
- **Multi-Select Variables**: Checkbox/dropdown with AND/OR logic
- **Range Variables**: Numeric ranges with sliders and operators
- **Simple Variables**: Number, text, percentage, currency, boolean
- **Predicate Variables**: Conditional logic (basic structure)

### 2. Condition Builder
- Visual IF-THEN-ELSE condition creation
- Variable and operator selection
- Mathematical expression building
- Real-time expression generation

### 3. Expression Management
- Real-time expression preview
- Validation and error reporting
- JSON import/export functionality
- Expression statistics and analysis

### 4. User Interface
- Modern Vue 3 + TypeScript architecture
- Tailwind CSS styling
- Responsive design
- Modal-based workflows

## Component Architecture

### Base Components (`src/components/ui/`)
- `BaseButton.vue`: Reusable button with variants
- `BaseInput.vue`: Form input with validation
- `BaseSelect.vue`: Dropdown selection
- `BaseModal.vue`: Modal dialog system

### Variable Components (`src/components/variables/`)
- `VariableItem.vue`: Individual variable display
- `VariableModal.vue`: Variable creation/editing
- `MultiSelectVariable.vue`: Multi-selection configuration
- `RangeVariable.vue`: Range/tier configuration
- `VariableValueDisplay.vue`: Type-specific value display

### Main Components
- `ExpressionEditor.vue`: Main application container
- `VariableManager.vue`: Variable list and management
- `ConditionBuilder.vue`: Condition creation interface
- `ExpressionDisplay.vue`: Expression preview and validation

## Composables Architecture

### `useExpressionBuilder`
Main orchestration composable that:
- Manages overall expression state
- Coordinates variable and condition managers
- Handles expression generation and validation
- Provides save/load functionality

### `useVariableManager`
Variable management composable that:
- Handles CRUD operations for variables
- Provides type-specific creation functions
- Manages variable validation
- Maintains variable relationships

### `useConditionBuilder`
Condition management composable that:
- Creates and manages conditions
- Handles operator and action definitions
- Validates condition logic
- Supports condition grouping

### `useJsonImportExport`
Import/export functionality that:
- Serializes expressions to JSON
- Validates imported JSON structure
- Handles file operations
- Manages export formats

## Type System

The application uses a comprehensive TypeScript type system:

### Core Types
- `Variable`: Base variable interface with type-specific extensions
- `Condition`: IF-THEN-ELSE condition structure
- `Expression`: Complete expression with metadata
- `Action`: Condition action definitions

### Variable Types
- `SimpleVariable`: Basic types (number, text, etc.)
- `MultiSelectVariable`: Multi-selection with options
- `RangeVariable`: Numeric ranges with tiers
- `PredicateVariable`: Conditional logic variables

## Development Workflow

### Adding New Variable Types
1. Define the type interface in `src/types/variable.ts`
2. Add creation function to `useVariableManager`
3. Create UI component in `src/components/variables/`
4. Update `VariableModal.vue` to handle the new type
5. Add display logic to `VariableValueDisplay.vue`

### Adding New Operators
1. Add operator to `ComparisonOperator` type
2. Update operator options in `useConditionBuilder`
3. Add operator logic to expression generation
4. Update validation rules

### Adding New Features
1. Define types in appropriate type files
2. Add logic to relevant composables
3. Create or update UI components
4. Add validation and error handling
5. Update documentation

## Testing Strategy

### Manual Testing
1. Create variables of each type
2. Build conditions using different operators
3. Test expression generation and validation
4. Verify JSON import/export functionality
5. Test error handling and edge cases

### Automated Testing (Future)
- Unit tests for composables
- Component testing with Vue Test Utils
- Integration tests for workflows
- E2E tests for complete scenarios

## Troubleshooting

### Common Issues

**TypeScript Errors**
- Ensure all imports use correct paths
- Check type definitions are properly exported
- Verify component props and emits are typed

**Styling Issues**
- Tailwind classes may need to be built
- Check for CSS conflicts
- Ensure responsive classes are applied

**Component Communication**
- Verify emit/prop relationships
- Check composable state management
- Ensure reactive updates are working

### Debug Tools
- Vue DevTools for component inspection
- Browser DevTools for network and console
- TypeScript compiler for type checking

## Performance Considerations

### Optimization Strategies
- Use `readonly()` for composable state
- Implement proper `key` attributes for lists
- Lazy load heavy components
- Debounce user input validation

### Memory Management
- Clean up event listeners in composables
- Use `onUnmounted` for cleanup
- Avoid memory leaks in watchers

## Future Enhancements

### Priority Features
1. Complete predicate variable implementation
2. Drag-and-drop condition reordering
3. Expression templates and presets
4. Advanced validation rules
5. Undo/redo functionality

### Advanced Features
1. Expression debugging and step-through
2. Performance analytics
3. Collaborative editing
4. Version control integration
5. Custom operator definitions

## Contributing

1. Follow TypeScript strict mode guidelines
2. Use composition API patterns
3. Maintain component modularity
4. Add proper type definitions
5. Update documentation for changes
