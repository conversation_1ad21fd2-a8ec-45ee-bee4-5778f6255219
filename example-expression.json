{"version": "1.0", "metadata": {"name": "Sales Commission Calculator", "description": "Calculate commission based on sales performance and loan types", "createdAt": "2024-01-15T10:30:00Z", "updatedAt": "2024-01-15T14:45:00Z", "exportedAt": "2024-01-15T14:45:00Z", "exportFormat": "full"}, "expression": {"id": "expr_1705320600000_abc123def", "name": "Sales Commission Calculator", "description": "Calculate commission based on sales performance and loan types", "variables": [{"id": "var_commission_rate", "name": "Commission Rate Type", "type": "multi-select", "description": "Type of commission rate to apply", "options": [{"value": "inclusive_rate", "label": "Inclusive Rate", "description": "Standard commission rate for all sales"}, {"value": "new_loan_rate", "label": "New Loan Rate", "description": "Higher rate for new customer loans"}, {"value": "repeat_loan_rate", "label": "Repeat Loan Rate", "description": "Rate for existing customer loans"}], "logicType": "OR", "selectedValues": ["inclusive_rate"], "minSelections": 1, "maxSelections": 1, "defaultValue": ["inclusive_rate"], "createdAt": "2024-01-15T10:30:00Z", "updatedAt": "2024-01-15T10:30:00Z"}, {"id": "var_performance_tier", "name": "Performance Tier", "type": "range", "description": "Sales performance tier based on total sales volume", "min": 0, "max": 1000000, "step": 1000, "operator": "between", "value": [50000, 100000], "tiers": [{"id": "tier_bronze", "name": "Bronze", "min": 0, "max": 50000, "value": "bronze_multiplier"}, {"id": "tier_silver", "name": "Silver", "min": 50001, "max": 100000, "value": "silver_multiplier"}, {"id": "tier_gold", "name": "Gold", "min": 100001, "max": 250000, "value": "gold_multiplier"}, {"id": "tier_platinum", "name": "Platinum", "min": 250001, "max": 1000000, "value": "platinum_multiplier"}], "defaultValue": 0, "createdAt": "2024-01-15T10:35:00Z", "updatedAt": "2024-01-15T10:35:00Z"}, {"id": "var_bonus_eligibility", "name": "Bonus Eligibility", "type": "predicate", "description": "Determines if agent is eligible for performance bonus", "conditions": [{"id": "cond_par_check", "if": {"variable": "par", "operator": "less_than", "value": 5}, "then": {"type": "assignment", "target": "var_bonus_eligibility", "value": true}, "else": {"type": "assignment", "target": "var_bonus_eligibility", "value": false}}], "dependencies": ["par"], "defaultValue": false, "createdAt": "2024-01-15T10:40:00Z", "updatedAt": "2024-01-15T10:40:00Z"}, {"id": "var_final_commission", "name": "Final Commission", "type": "currency", "description": "Calculated final commission amount", "defaultValue": 0, "value": 0, "createdAt": "2024-01-15T10:45:00Z", "updatedAt": "2024-01-15T10:45:00Z"}], "conditions": [{"id": "cond_main_calculation", "variable": "total_sales", "operator": "is_not_empty", "value": null, "thenAction": {"type": "calculation", "target": "var_final_commission", "expression": "total_sales * commission_rate * performance_multiplier"}, "elseAction": {"type": "assignment", "target": "var_final_commission", "value": 0}, "createdAt": "2024-01-15T11:00:00Z", "updatedAt": "2024-01-15T11:00:00Z"}, {"id": "cond_bonus_application", "variable": "var_bonus_eligibility", "operator": "equals", "value": true, "thenAction": {"type": "calculation", "target": "var_final_commission", "expression": "var_final_commission * 1.1"}, "createdAt": "2024-01-15T11:05:00Z", "updatedAt": "2024-01-15T11:05:00Z"}], "generatedExpression": "IF(total_sales IS NOT EMPTY) THEN var_final_commission = total_sales * commission_rate * performance_multiplier ELSE var_final_commission = 0 AND IF(var_bonus_eligibility = true) THEN var_final_commission = var_final_commission * 1.1", "metadata": {"createdAt": "2024-01-15T10:30:00Z", "updatedAt": "2024-01-15T14:45:00Z", "tags": ["commission", "sales", "performance"], "category": "financial"}, "version": "1.0"}, "schema": {"version": "1.0", "variableTypes": ["number", "text", "percentage", "currency", "boolean", "multi-select", "range", "predicate"], "operatorTypes": ["equals", "not_equals", "greater_than", "less_than", "greater_equal", "less_equal", "is_empty", "is_not_empty", "contains", "not_contains", "in", "not_in"], "actionTypes": ["assignment", "calculation", "value"]}}