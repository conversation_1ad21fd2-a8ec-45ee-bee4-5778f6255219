Project: we are trying to build a rule engine that will allow users to create complex expressions using variables and conditions with a visual interface.

GOAl: the visual interface should be dynamic and generate correct json structure and final expr using the users input.Do not hard code any thing as the process should be user driven.

context
System Variables for the editor (reserved variables)
- Total Sales
- PAR
- Portfolio Size
- Number of New Loans
- Number of Repeat Loans
- Loan Products( e.g CS, LBF, SME)
others will be added later

user can also create variables to be used in setting up the actions to be performed 

Test scenerio  
 
Create EndMonthCommission
 it should have the following user variables 
 - inclusive rate 
        type is a percentage 
        description is rate used irrespective for the loan type
-  new loan rate 
        type is a percentage 
        description is rate used for new loans
-  repeat loan rate 
        type is a percentage 
        description is rate used for repeat loans

- final commission
    type is a number
    description contains the final commission to be awarded to the sales associate

    conditions 
    1. if inclusive rate is not empty then final commission is equal to total sales * inclusive rate else if new loan rate is not empty then final commission is equal to total sales * new loan rate else if repeat loan rate is not empty then final commission is equal to total sales * repeat loan rate else if all the above cases are empty then final commission is equal to 0

    final expression should be 
    IF(inclusive rate is not empty) THEN final commission = total sales * inclusive rate ELSE IF(new loan rate is not empty) THEN final commission = total sales * new loan rate ELSE IF(repeat loan rate is not empty) THEN final commission = total sales * repeat loan rate ELSE final commission = 0
    


Create Weekly Loan Type Incentive
 it should have the following user variables 
 - inclusive rate 
        type is a percentage 
        description is rate used irrespective for the loan type
-  new loan rate 
        type is a percentage 
        description is rate used for new loans
-  repeat loan rate 
        type is a percentage 
        description is rate used for repeat loans
- fixed Amount
        type is a number 
        description is the fixed amount to be awarded to the sales associate

- final commission
    type is a number
    description contains the final commission to be awarded to the sales associate

    conditions 
    1. if fixed amount is not empty then final commission is equal to fixed amount else if Total Sales is less than 100000 then final commission is equal to 0 else if Total Sales is less than 200000 then final commission is equal to 2000 else if Total Sales is less than 300000 then final commission is equal to 4000 else if Total Sales is less than 400000 then final commission is equal to 6000 else final commission is equal to 8000

    2. if total sales is greater than or equal to weekly target then final commission is equal to total sales * inclusive rate else final commission is equal to 0

    3. if loan type is Buyoff then final commission is equal to total sales * 1.5% else if loan type is Top-up then final commission is equal to total sales * 1% else if loan type is New then final commission is equal to total sales * 2% else final commission is equal to 0

    final expression should be 
    IF(fixed amount is not empty) THEN final commission = fixed amount ELSE IF(Total Sales < 100000) THEN final commission = 0 ELSE IF(Total Sales < 200000) THEN final commission = 2000 ELSE IF(Total Sales < 300000) THEN final commission = 4000 ELSE IF(Total Sales < 400000) THEN final commission = 6000 ELSE final commission = 8000
    AND IF(Total Sales >= weekly target) THEN final commission = total sales * inclusive rate ELSE final commission = 0
    AND IF(loan type = Buyoff) THEN final commission = total sales * 1.5% ELSE IF(loan type = Top-up) THEN final commission = total sales * 1% ELSE IF(loan type = New) THEN final commission = total sales * 2% ELSE final commission = 0



create Portfolio Commission 
it should have the following user variables 
- portfolio size
   type is a number 
   description is the size of the portfolio
- portfolio target
    type is a number 
    description is the portfolio target for the portfolio
- portfolio sales
    type is a number 
    description is the portfolio sales for the portfolio
- PAR Days
    type is a number 
    description is the  number of days in PAR 
- PAR Percentage
    type is a percentage 
    description is the percentage of PAR in the portfolio that the associate should meet
- fixed amount
    type is a number 
    description is the fixed amount to be awarded to the sales associate
- commission rate
    type is a percentage 
    description is the commission rate to be applied to the portfolio
- final commission
        type is a number
        description contains the final commission to be awarded to the sales associate
- portfolio category (multi select)
        type is a multi select 
        description is the category of the portfolio
        options are Fixed Amount, Rate Based, Target Based
- loan type (multi select)
        type is a multi select 
        description is the type of loan
        options are Buyoff, Top-up, New

conditions 
1. if portfolio category is Fixed Amount and PAR(PAR Days) < PAR Percentage and Portfolio Size is in the range of {100,000, 200,000 } then final commission is equal to 3000
2. if portfolio category is Rate Based and  PAR(PAR Days) < PAR Percentage then 
    final commission is equal to portfolio size * commission rate
3. if portfolio category is Target Based and net sales is greater than portfolio target and PAR(PAR Days) is less than par percentage then final commission is equal to portfolio size * commission rate else final commission is equal to 0


final expression should be 
IF(portfolio category = Fixed Amount AND PAR(PAR Days) < PAR Percentage AND Portfolio Size is in the range of {100,000, 200,000 }) THEN final commission = 3000 ELSE IF(portfolio category = Rate Based AND PAR(PAR Days) < PAR Percentage) THEN final commission = portfolio size * commission rate ELSE IF(portfolio category = Target Based AND net sales > portfolio target AND PAR(PAR Days) < par percentage) THEN final commission = portfolio size * commission rate ELSE final commission = 0


  