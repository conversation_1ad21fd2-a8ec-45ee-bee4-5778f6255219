# Expression Editor - Vue 3 + TypeScript

A modern, component-based expression editor built with Vue 3, TypeScript, and Tailwind CSS. This application allows users to create complex expressions using variables and conditions with a visual interface.

## Features

### Core Functionality
- **Visual Expression Building**: Create complex expressions using a drag-and-drop interface
- **Variable Management**: Support for multiple variable types with type-specific configurations
- **Condition Builder**: Visual IF-THEN-ELSE condition creation with logical operators
- **Real-time Validation**: Instant feedback on expression validity and errors
- **JSON Import/Export**: Save and load expression configurations

### Variable Types Supported

#### 1. Multi-Selection Variables (Scenario 1)
- Checkbox/multi-select interface
- AND/OR logic between selected values
- Configurable minimum/maximum selections
- Dynamic option management

#### 2. Range/Tier Variables (Scenario 2)
- Numeric range inputs with sliders
- Multiple operators (equals, greater than, less than, between, etc.)
- Tier-based categorization
- Visual range representation

#### 3. Predicate Variables (Scenario 3)
- Conditional logic based on other variables
- Nested IF-THEN-ELSE expressions
- Dependency tracking and circular dependency prevention
- Complex conditional trees

#### 4. Simple Variables
- Number, text, percentage, currency, boolean types
- Type-specific input validation
- Default value configuration

### Technical Features
- **TypeScript**: Full type safety throughout the application
- **Composition API**: Modern Vue 3 patterns with reusable composables
- **Component Architecture**: Modular, reusable components
- **Tailwind CSS**: Utility-first styling with consistent design system
- **Pinia**: State management for complex application state
- **Validation**: Comprehensive input validation and error handling

## Project Structure

```
src/
├── components/
│   ├── ExpressionEditor.vue          # Main editor container
│   ├── VariableManager.vue           # Variable list and management
│   ├── ConditionBuilder.vue          # Condition creation interface
│   ├── ExpressionDisplay.vue         # Expression preview and validation
│   ├── ImportExportModal.vue         # JSON import/export functionality
│   ├── variables/
│   │   ├── VariableItem.vue          # Individual variable display
│   │   ├── VariableModal.vue         # Variable creation/edit modal
│   │   ├── VariableValueDisplay.vue  # Type-specific value display
│   │   ├── MultiSelectVariable.vue   # Multi-selection configuration
│   │   └── RangeVariable.vue         # Range/tier configuration
│   └── ui/
│       ├── BaseButton.vue            # Reusable button component
│       ├── BaseInput.vue             # Reusable input component
│       ├── BaseSelect.vue            # Reusable select component
│       └── BaseModal.vue             # Reusable modal component
├── composables/
│   ├── useExpressionBuilder.ts       # Main expression building logic
│   ├── useVariableManager.ts         # Variable management logic
│   ├── useConditionBuilder.ts        # Condition building logic
│   └── useJsonImportExport.ts        # JSON serialization logic
├── types/
│   ├── variable.ts                   # Variable-related types
│   ├── condition.ts                  # Condition-related types
│   ├── expression.ts                 # Expression-related types
│   └── index.ts                      # Type exports
└── utils/
    └── constants.ts                  # Application constants
```

## Getting Started

### Prerequisites
- Node.js 16+ and npm/yarn
- Modern web browser with ES6+ support

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Build for production**:
   ```bash
   npm run build
   ```

### Development Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Usage Examples

### Creating a Multi-Select Variable
1. Click "Add Variable" in the Variables section
2. Select "Multi-Select" as the variable type
3. Add options with labels and values
4. Choose AND/OR logic type
5. Set minimum/maximum selection constraints

### Building Range Variables
1. Create a new variable with "Range" type
2. Set minimum and maximum values
3. Choose an operator (equals, greater than, between, etc.)
4. Optionally define tiers for categorization
5. Use the slider or input fields to set values

### Creating Conditions
1. Click "Add Condition" in the Conditions section
2. Select a variable for the IF clause
3. Choose a comparison operator
4. Set the comparison value
5. Define THEN and ELSE actions
6. Chain multiple conditions with AND/OR logic

### JSON Import/Export
1. **Export**: Click "Export" → Choose format → Download or copy
2. **Import**: Click "Import" → Upload file or paste JSON → Preview → Import

## JSON Structure

The application exports expressions in a structured JSON format:

```json
{
  "version": "1.0",
  "metadata": {
    "name": "Expression Name",
    "description": "Expression description",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "variables": [
    {
      "id": "var_1",
      "name": "Variable Name",
      "type": "multi-select",
      "options": [...],
      "selectedValues": [...],
      "logicType": "OR"
    }
  ],
  "conditions": [
    {
      "id": "cond_1",
      "variable": "var_1",
      "operator": "equals",
      "value": "some_value",
      "thenAction": {...},
      "elseAction": {...}
    }
  ],
  "expression": "Generated expression string"
}
```

## Architecture Decisions

### Composition API
- **Why**: Better TypeScript integration, improved code reuse, and cleaner logic organization
- **Implementation**: All logic is organized into composables for maximum reusability

### Component-Based Design
- **Why**: Maintainable, testable, and reusable code
- **Implementation**: Each variable type has its own component with specific configuration UI

### TypeScript Integration
- **Why**: Type safety, better developer experience, and reduced runtime errors
- **Implementation**: Strict TypeScript configuration with comprehensive type definitions

### Tailwind CSS
- **Why**: Rapid development, consistent design system, and small bundle size
- **Implementation**: Utility-first approach with custom component classes

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes with proper TypeScript types
4. Add tests for new functionality
5. Ensure all tests pass: `npm run test`
6. Submit a pull request

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

MIT License - see LICENSE file for details
