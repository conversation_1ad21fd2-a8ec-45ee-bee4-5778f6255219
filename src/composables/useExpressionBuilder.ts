import { ref, computed, readonly, watch } from 'vue'
import type { 
  Expression, 
  ExpressionValidation, 
  ExpressionError, 
  ExpressionWarning,
  Variable
} from '@/types'
import { useVariableManager } from './useVariableManager'
import { useConditionBuilder } from './useConditionBuilder'

export function useExpressionBuilder() {
  const variableManager = useVariableManager()
  const conditionBuilder = useConditionBuilder()

  const expression = ref<Expression>({
    id: generateExpressionId(),
    name: 'Individual Sales Procedure',
    description: 'Main Execution Steps for the current Commission Structure',
    variables: [],
    conditions: [],
    generatedExpression: '',
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    version: '1.0'
  })

  const isModified = ref(false)
  const lastSaved = ref<string | null>(null)

  // Computed properties
  const isValid = computed(() => {
    const validation = validateExpression()
    return validation.isValid
  })

  const validationErrors = computed(() => {
    const validation = validateExpression()
    return validation.errors
  })

  const validationWarnings = computed(() => {
    const validation = validateExpression()
    return validation.warnings
  })

  const generatedExpression = computed(() => {
    return generateExpressionString()
  })

  // Watch for changes to mark as modified
  watch([
    () => variableManager.variables.value,
    () => conditionBuilder.conditions.value,
    () => expression.value.name,
    () => expression.value.description
  ], () => {
    isModified.value = true
    expression.value.metadata.updatedAt = new Date().toISOString()
    expression.value.generatedExpression = generateExpressionString()
  }, { deep: true })

  // Expression generation
  function generateExpressionString(): string {
    const conditions = conditionBuilder.conditions.value
    if (conditions.length === 0) {
      return 'No conditions defined'
    }

    const expressionParts: string[] = []

    conditions.forEach((condition, index) => {
      // Get variable name, checking both user variables and reserved variables
      let variableName = '[No Variable Selected]'
      if (condition.variable) {
        const variable = variableManager.getVariable(condition.variable)
        if (variable) {
          variableName = variable.name
        } else {
          // Check if it's a reserved variable
          const reservedVar = variableManager.reservedVariables.value.find(rv => rv.value === condition.variable)
          variableName = reservedVar ? reservedVar.name : condition.variable
        }
      }

      let conditionStr = `IF(${variableName} ${getOperatorSymbol(condition.operator)}`
      
      if (!['is_empty', 'is_not_empty'].includes(condition.operator)) {
        conditionStr += ` ${condition.value}`
      }
      conditionStr += ')'

      // Add THEN action
      if (condition.thenAction) {
        conditionStr += ` THEN ${formatAction(condition.thenAction)}`
      }

      // Add ELSE action
      if (condition.elseAction) {
        conditionStr += ` ELSE ${formatAction(condition.elseAction)}`
      }

      expressionParts.push(conditionStr)
    })

    const finalExpression = expressionParts.join(' AND ')

    // Debug logging (can be removed in production)
    console.log('Generated Expression:', finalExpression)
    console.log('Conditions:', conditions)

    return finalExpression
  }

  // Helper function to get variable name from ID (moved outside formatAction for reuse)
  function getVariableName(variableId: string): string {
    if (!variableId) return '?'

    // First check user variables
    const variable = variableManager.getVariable(variableId)
    if (variable) {
      return variable.name
    }

    // Then check reserved variables
    const reservedVar = variableManager.reservedVariables.value.find(rv => rv.value === variableId)
    if (reservedVar) {
      return reservedVar.name
    }

    // Finally check if it's a variable name that needs to be resolved to a variable
    const variableByName = variableManager.allVariables.value.find(v => v.name === variableId)
    if (variableByName) {
      return variableByName.name
    }

    return variableId
  }

  function formatAction(action: any): string {
    console.log('Formatting action:', action)

    switch (action.type) {
      case 'assignment':
        const targetName = getVariableName(action.target)
        if (action.expression) {
          return `${targetName} = ${action.expression}`
        } else if (action.value !== undefined) {
          return `${targetName} = ${action.value}`
        }
        return `${targetName} = ?`

      case 'calculation':
        const calcTargetName = getVariableName(action.target)
        let expression = action.expression || '?'

        // Parse and enhance the expression to resolve variable names
        if (expression && expression !== '?') {
          // Check if it's a simple variable that should be multiplied by Total Sales
          if (!/[+\-*/]/.test(expression)) {
            const expressionVariable = variableManager.getVariable(expression) ||
                                     variableManager.allVariables.value.find(v => v.name === expression)
            if (expressionVariable && (expressionVariable.type === 'percentage' || expressionVariable.name.toLowerCase().includes('rate'))) {
              expression = `${expressionVariable.name} * Total Sales`
            } else {
              expression = enhanceExpression(expression)
            }
          } else {
            // Replace variable IDs/names with proper display names in the expression
            expression = enhanceExpression(expression)
          }
        }

        return `${calcTargetName} = ${expression}`

      case 'value':
        // For value type, if there's a target, show assignment, otherwise just the value
        if (action.target) {
          const valueTargetName = getVariableName(action.target)
          // Handle the case where both expression and value exist (prioritize expression if it's a variable name)
          let assignmentValue
          if (action.expression && action.expression !== '') {
            // Check if expression contains operators (making it a calculation)
            if (/[+\-*/]/.test(action.expression)) {
              // It's a calculation expression, enhance it
              assignmentValue = enhanceExpression(action.expression)
            } else {
              // Check if expression is a variable name
              const expressionVariable = variableManager.getVariable(action.expression) ||
                                       variableManager.allVariables.value.find(v => v.name === action.expression)

              if (expressionVariable) {
                // Special case: if it's a rate/percentage variable and we're in a commission context,
                // automatically multiply by Total Sales (unless it already contains an operator)
                if ((expressionVariable.type === 'percentage' || expressionVariable.name.toLowerCase().includes('rate'))
                    && !action.expression.includes('*') && !action.expression.includes('+')
                    && !action.expression.includes('-') && !action.expression.includes('/')) {
                  assignmentValue = `${expressionVariable.name} * Total Sales`
                } else {
                  assignmentValue = expressionVariable.name
                }
              } else {
                // Check if it's a simple rate word that should be enhanced
                if ((action.expression.toLowerCase().includes('rate') || action.expression.toLowerCase() === 'rate')
                    && !action.expression.includes('*')) {
                  assignmentValue = `${action.expression} * Total Sales`
                } else {
                  assignmentValue = action.expression
                }
              }
            }
          } else if (action.value !== undefined && action.value !== '') {
            assignmentValue = action.value
          } else {
            assignmentValue = '?'
          }
          return `${valueTargetName} = ${assignmentValue}`
        }
        return String(action.value || '?')

      default:
        console.log('Unknown action type:', action.type)
        return '?'
    }
  }

  // Helper function to enhance expressions by resolving variable names
  function enhanceExpression(expression: string): string {
    if (!expression) return '?'

    // Split the expression by operators while preserving them
    const parts = expression.split(/([+\-*/\s]+)/)

    return parts.map(part => {
      const trimmedPart = part.trim()

      // Skip operators and empty parts
      if (!trimmedPart || /^[+\-*/\s]+$/.test(trimmedPart)) {
        return part
      }

      // Check if it's a number
      if (!isNaN(Number(trimmedPart))) {
        return part
      }

      // Try to resolve as variable name
      const resolvedName = getVariableName(trimmedPart)
      return resolvedName !== trimmedPart ? resolvedName : part
    }).join('')
  }

  function getOperatorSymbol(operator: string): string {
    const operators: Record<string, string> = {
      'equals': '=',
      'not_equals': '!=',
      'greater_than': '>',
      'less_than': '<',
      'greater_equal': '>=',
      'less_equal': '<=',
      'is_empty': 'IS EMPTY',
      'is_not_empty': 'IS NOT EMPTY',
      'contains': 'CONTAINS',
      'not_contains': 'NOT CONTAINS',
      'in': 'IN',
      'not_in': 'NOT IN'
    }
    return operators[operator] || operator
  }

  // Validation
  function validateExpression(): ExpressionValidation {
    const errors: ExpressionError[] = []
    const warnings: ExpressionWarning[] = []
    const circularDependencies: string[] = []
    const unreferencedVariables: string[] = []

    // Validate expression name
    if (!expression.value.name.trim()) {
      errors.push({
        type: 'syntax',
        message: 'Expression name is required'
      })
    }

    // Validate variables
    variableManager.variables.value.forEach((variable:any) => {
      const variableErrors = variableManager.validateVariable(variable)
      variableErrors.forEach(error => {
        errors.push({
          type: 'reference',
          message: error,
          location: { variableId: variable.id }
        })
      })
    })

    // Validate conditions
    const availableVariableIds = variableManager.allVariables.value.map(v => v.id)
    const conditionValidation = conditionBuilder.validateAllConditions(availableVariableIds)
    
    conditionValidation.errors.forEach(error => {
      errors.push({
        type: 'logic',
        message: error
      })
    })

    conditionValidation.warnings.forEach(warning => {
      warnings.push({
        type: 'unused',
        message: warning
      })
    })

    // Check for circular dependencies in predicate variables
    const predicateVariables = variableManager.variables.value.filter(v => v.type === 'predicate')
    predicateVariables.forEach((variable: any) => {
      const deps = findCircularDependencies(variable.id, variableManager.variables.value as any)
      if (deps.length > 0) {
        circularDependencies.push(...deps)
        errors.push({
          type: 'logic',
          message: `Circular dependency detected in variable "${variable.name}"`,
          location: { variableId: variable.id }
        })
      }
    })

    // Find unreferenced variables
    const referencedVariables = new Set<string>()
    conditionBuilder.conditions.value.forEach(condition => {
      // Only add non-empty variables to referenced set
      if (condition.variable && condition.variable.trim() !== '') {
        referencedVariables.add(condition.variable)
      }
      if (condition.thenAction?.target && condition.thenAction.target.trim() !== '') {
        referencedVariables.add(condition.thenAction.target)
      }
      if (condition.elseAction?.target && condition.elseAction.target.trim() !== '') {
        referencedVariables.add(condition.elseAction.target)
      }
    })

    variableManager.userVariables.value.forEach(variable => {
      if (!referencedVariables.has(variable.id)) {
        unreferencedVariables.push(variable.id)
        warnings.push({
          type: 'unused',
          message: `Variable "${variable.name}" is not used in any conditions`,
          location: { variableId: variable.id }
        })
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      circularDependencies,
      unreferencedVariables
    }
  }

  function findCircularDependencies(variableId: string, variables: Variable[], visited: Set<string> = new Set()): string[] {
    if (visited.has(variableId)) {
      return [variableId]
    }

    const variable = variables.find(v => v.id === variableId)
    if (!variable || variable.type !== 'predicate') {
      return []
    }

    visited.add(variableId)
    const predicateVar = variable as any // PredicateVariable type
    
    for (const dep of predicateVar.dependencies || []) {
      const circular = findCircularDependencies(dep, variables, new Set(visited))
      if (circular.length > 0) {
        return [variableId, ...circular]
      }
    }

    return []
  }

  // Expression management
  function updateExpressionName(name: string): void {
    expression.value.name = name
    isModified.value = true
  }

  function updateExpressionDescription(description: string): void {
    expression.value.description = description
    isModified.value = true
  }

  function saveExpression(): void {
    expression.value.variables = [...variableManager.variables.value] as any
    expression.value.conditions = [...conditionBuilder.conditions.value]
    expression.value.generatedExpression = generateExpressionString()
    expression.value.metadata.updatedAt = new Date().toISOString()
    
    isModified.value = false
    lastSaved.value = new Date().toISOString()
  }

  function resetExpression(): void {
    expression.value = {
      id: generateExpressionId(),
      name: 'Individual Sales Procedure',
      description: 'Main Execution Steps for the current Commission Structure',
      variables: [],
      conditions: [],
      generatedExpression: '',
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      version: '1.0'
    }
    
    variableManager.clearVariables()
    conditionBuilder.clearConditions()
    isModified.value = false
    lastSaved.value = null
  }

  function loadExpression(expressionData: Expression): void {
    expression.value = { ...expressionData }
    
    // Load variables
    variableManager.clearVariables()
    expressionData.variables.forEach(variable => {
      variableManager.addVariable(variable)
    })
    
    // Load conditions
    conditionBuilder.clearConditions()
    expressionData.conditions.forEach(condition => {
      conditionBuilder.addCondition(condition)
    })
    
    isModified.value = false
    lastSaved.value = expressionData.metadata.updatedAt
  }

  // Utility functions
  function generateExpressionId(): string {
    return `expr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  return {
    // State
    expression: readonly(expression),
    isModified: readonly(isModified),
    lastSaved: readonly(lastSaved),

    // Computed
    isValid,
    validationErrors,
    validationWarnings,
    generatedExpression,

    // Sub-composables
    variableManager,
    conditionBuilder,

    // Expression management
    updateExpressionName,
    updateExpressionDescription,
    saveExpression,
    resetExpression,
    loadExpression,

    // Validation
    validateExpression
  }
}
