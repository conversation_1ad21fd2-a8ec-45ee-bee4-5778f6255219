import { ref, computed, readonly } from 'vue'
import type {
  Condition,
  ConditionGroup,
  ConditionSet,
  ComparisonOperator,
  Action,
  MathOperator,
  CalculationExpression,
  ConditionValidation
} from '@/types'

export function useConditionBuilder() {
  const conditions = ref<Condition[]>([])
  const conditionGroups = ref<ConditionGroup[]>([])
  const conditionSets = ref<ConditionSet[]>([])
  const selectedConditionId = ref<string | null>(null)
  const selectedConditionSetId = ref<string | null>(null)

  // Available operators
  const comparisonOperators: { value: ComparisonOperator; label: string }[] = [
    { value: 'equals', label: '=' },
    { value: 'not_equals', label: '!=' },
    { value: 'greater_than', label: '>' },
    { value: 'less_than', label: '<' },
    { value: 'greater_equal', label: '>=' },
    { value: 'less_equal', label: '<=' },
    { value: 'is_empty', label: 'is Empty' },
    { value: 'is_not_empty', label: 'is Not Empty' },
    { value: 'contains', label: 'contains' },
    { value: 'not_contains', label: 'does not contain' },
    { value: 'in', label: 'is in' },
    { value: 'not_in', label: 'is not in' },
    { value: 'between', label: 'is between' },
    { value: 'not_between', label: 'is not between' }
  ]

  const mathOperators: { value: MathOperator; label: string }[] = [
    { value: '+', label: '+' },
    { value: '-', label: '-' },
    { value: '*', label: '×' },
    { value: '/', label: '÷' },
    { value: '%', label: '%' },
    { value: '^', label: '^' }
  ]

  // Computed properties
  const selectedCondition = computed(() => 
    conditions.value.find(c => c.id === selectedConditionId.value) || null
  )

  const hasConditions = computed(() => conditions.value.length > 0)

  const conditionCount = computed(() => conditions.value.length)

  // Condition creation
  function createCondition(
    variable: string,
    operator: ComparisonOperator,
    value: any,
    thenAction: Action,
    elseAction?: Action
  ): Condition {
    return {
      id: generateConditionId(),
      variable,
      operator,
      value,
      thenAction,
      elseAction,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  function createAction(
    type: 'assignment' | 'calculation' | 'value',
    target?: string,
    expression?: string,
    value?: any
  ): Action {
    return {
      type,
      target,
      expression,
      value
    }
  }

  function createCalculationExpression(
    left: string | number,
    operator: MathOperator,
    right: string | number
  ): CalculationExpression {
    return {
      left,
      operator,
      right
    }
  }

  // CRUD operations
  function addCondition(condition: Condition): void {
    conditions.value.push(condition)
  }

  function updateCondition(id: string, updates: Partial<Condition>): boolean {
    const index = conditions.value.findIndex(c => c.id === id)
    if (index === -1) return false

    conditions.value[index] = {
      ...conditions.value[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }
    return true
  }

  function deleteCondition(id: string): boolean {
    const index = conditions.value.findIndex(c => c.id === id)
    if (index === -1) return false

    conditions.value.splice(index, 1)
    if (selectedConditionId.value === id) {
      selectedConditionId.value = null
    }
    return true
  }

  function getCondition(id: string): Condition | undefined {
    return conditions.value.find(c => c.id === id)
  }

  // Condition groups
  function createConditionGroup(
    type: 'IF' | 'ELSE_IF' | 'ELSE',
    conditions: Condition[] = [],
    logicType: 'AND' | 'OR' = 'AND'
  ): ConditionGroup {
    return {
      id: generateConditionId(),
      type,
      conditions,
      logicType
    }
  }

  function addConditionGroup(group: ConditionGroup): void {
    conditionGroups.value.push(group)
  }

  function updateConditionGroup(id: string, updates: Partial<ConditionGroup>): boolean {
    const index = conditionGroups.value.findIndex(g => g.id === id)
    if (index === -1) return false

    conditionGroups.value[index] = {
      ...conditionGroups.value[index],
      ...updates
    }
    return true
  }

  function deleteConditionGroup(id: string): boolean {
    const index = conditionGroups.value.findIndex(g => g.id === id)
    if (index === -1) return false

    conditionGroups.value.splice(index, 1)
    return true
  }

  // Condition Set Management
  function createConditionSet(
    name: string,
    description?: string,
    isIndependent: boolean = false,
    priority: number = 0
  ): ConditionSet {
    return {
      id: generateConditionSetId(),
      name,
      description,
      conditions: [],
      isIndependent,
      priority,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  function addConditionSet(conditionSet: ConditionSet): void {
    conditionSets.value.push(conditionSet)
  }

  function updateConditionSet(id: string, updates: Partial<ConditionSet>): boolean {
    const index = conditionSets.value.findIndex(cs => cs.id === id)
    if (index === -1) return false

    conditionSets.value[index] = {
      ...conditionSets.value[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }
    return true
  }

  function deleteConditionSet(id: string): boolean {
    const index = conditionSets.value.findIndex(cs => cs.id === id)
    if (index === -1) return false

    conditionSets.value.splice(index, 1)
    return true
  }

  function addConditionToSet(setId: string, condition: Condition): boolean {
    const conditionSet = conditionSets.value.find(cs => cs.id === setId)
    if (!conditionSet) return false

    conditionSet.conditions.push(condition)
    conditionSet.updatedAt = new Date().toISOString()
    return true
  }

  function removeConditionFromSet(setId: string, conditionId: string): boolean {
    const conditionSet = conditionSets.value.find(cs => cs.id === setId)
    if (!conditionSet) return false

    const index = conditionSet.conditions.findIndex(c => c.id === conditionId)
    if (index === -1) return false

    conditionSet.conditions.splice(index, 1)
    conditionSet.updatedAt = new Date().toISOString()
    return true
  }

  // Validation
  function validateCondition(condition: Condition, availableVariables: string[]): ConditionValidation {
    const errors: string[] = []
    const warnings: string[] = []

    // Check if variable is selected
    if (!condition.variable || condition.variable.trim() === '') {
      errors.push('Variable must be selected')
    } else if (!availableVariables.includes(condition.variable)) {
      errors.push(`Variable "${condition.variable}" does not exist`)
    }

    // Check if value is appropriate for operator
    if (['is_empty', 'is_not_empty'].includes(condition.operator) && condition.value !== null) {
      warnings.push('Value is ignored for empty/not empty operators')
    }

    if (!['is_empty', 'is_not_empty'].includes(condition.operator) && (condition.value === null || condition.value === undefined)) {
      errors.push('Value is required for this operator')
    }

    // Validate actions
    if (condition.thenAction.type === 'assignment' && !condition.thenAction.target) {
      errors.push('Assignment action requires a target variable')
    }

    if (condition.thenAction.type === 'calculation' && !condition.thenAction.expression) {
      errors.push('Calculation action requires an expression')
    }

    if (condition.thenAction.type === 'value' && condition.thenAction.value === undefined) {
      errors.push('Value action requires a value')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  function validateAllConditions(availableVariables: string[]): ConditionValidation {
    const allErrors: string[] = []
    const allWarnings: string[] = []

    conditions.value.forEach((condition, index) => {
      const validation = validateCondition(condition, availableVariables)
      allErrors.push(...validation.errors.map(error => `Condition ${index + 1}: ${error}`))
      allWarnings.push(...validation.warnings.map(warning => `Condition ${index + 1}: ${warning}`))
    })

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings
    }
  }

  // Utility functions
  function generateConditionId(): string {
    return `cond_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  function generateConditionSetId(): string {
    return `condset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  function clearConditions(): void {
    conditions.value = []
    conditionGroups.value = []
    conditionSets.value = []
    selectedConditionId.value = null
    selectedConditionSetId.value = null
  }

  function selectCondition(id: string): void {
    selectedConditionId.value = id
  }

  function deselectCondition(): void {
    selectedConditionId.value = null
  }

  function duplicateCondition(id: string): Condition | null {
    const condition = getCondition(id)
    if (!condition) return null

    const duplicate = {
      ...condition,
      id: generateConditionId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    addCondition(duplicate)
    return duplicate
  }

  function moveCondition(fromIndex: number, toIndex: number): boolean {
    if (fromIndex < 0 || fromIndex >= conditions.value.length || 
        toIndex < 0 || toIndex >= conditions.value.length) {
      return false
    }

    const condition = conditions.value.splice(fromIndex, 1)[0]
    conditions.value.splice(toIndex, 0, condition)
    return true
  }

  return {
    // State
    conditions: readonly(conditions),
    conditionGroups: readonly(conditionGroups),
    conditionSets: readonly(conditionSets),
    selectedConditionId: readonly(selectedConditionId),
    selectedConditionSetId: readonly(selectedConditionSetId),

    // Constants
    comparisonOperators,
    mathOperators,

    // Computed
    selectedCondition,
    hasConditions,
    conditionCount,

    // Creation functions
    createCondition,
    createAction,
    createCalculationExpression,
    createConditionGroup,
    createConditionSet,

    // CRUD operations
    addCondition,
    updateCondition,
    deleteCondition,
    getCondition,
    addConditionGroup,
    updateConditionGroup,
    deleteConditionGroup,
    addConditionSet,
    updateConditionSet,
    deleteConditionSet,
    addConditionToSet,
    removeConditionFromSet,

    // Validation
    validateCondition,
    validateAllConditions,

    // Utility
    clearConditions,
    selectCondition,
    deselectCondition,
    duplicateCondition,
    moveCondition
  }
}
