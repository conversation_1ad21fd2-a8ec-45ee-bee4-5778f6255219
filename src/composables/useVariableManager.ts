import { ref, computed, readonly } from 'vue'
import type { 
  Variable, 
  SimpleVariable, 
  MultiSelectVariable, 
  RangeVariable, 
  PredicateVariable,
  VariableType,
  SelectOption,
} from '@/types'
import {RESERVED_VARIABLES} from '@/types'

export function useVariableManager() {
  const variables = ref<Variable[]>([])
  const selectedVariableId = ref<string | null>(null)

  // Computed properties
  const userVariables = computed(() => 
    variables.value.filter(v => !RESERVED_VARIABLES.some(rv => rv.value === v.id))
  )

  const reservedVariables = computed(() => RESERVED_VARIABLES)

  const allVariables = computed(() => [
    ...reservedVariables.value.map(rv => ({
      id: rv.value,
      name: rv.name,
      type: rv.type,
      description: rv.description,
      defaultValue: 0,
      value: 0
    } as SimpleVariable)),
    ...variables.value
  ])

  const selectedVariable = computed(() => 
    variables.value.find(v => v.id === selectedVariableId.value) || null
  )

  const variableOptions = computed(() => 
    allVariables.value.map(v => ({
      value: v.id,
      label: v.name,
      description: v.description
    }))
  )

  const userVariableOptions = computed(() => 
    userVariables.value.map(v => ({
      value: v.id,
      label: v.name,
      description: v.description
    }))
  )

  // Variable creation functions
  function createSimpleVariable(
    name: string, 
    type: 'number' | 'text' | 'percentage' | 'currency' | 'boolean',
    description?: string,
    defaultValue?: any
  ): SimpleVariable {
    return {
      id: generateId(),
      name,
      type,
      description,
      defaultValue: defaultValue ?? getDefaultValueForType(type),
      value: defaultValue ?? getDefaultValueForType(type),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  function createMultiSelectVariable(
    name: string,
    options: SelectOption[],
    description?: string
  ): MultiSelectVariable {
    return {
      id: generateId(),
      name,
      type: 'multi-select',
      description,
      options,
      logicType: 'OR',
      selectedValues: [],
      defaultValue: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  function createRangeVariable(
    name: string,
    min: number,
    max: number,
    description?: string
  ): RangeVariable {
    return {
      id: generateId(),
      name,
      type: 'range',
      description,
      min,
      max,
      step: 1,
      operator: 'between',
      value: min,
      defaultValue: min,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  function createPredicateVariable(
    name: string,
    description?: string,
    defaultValue?: any
  ): PredicateVariable {
    return {
      id: generateId(),
      name,
      type: 'predicate',
      description,
      conditions: [],
      dependencies: [],
      defaultValue: defaultValue ?? null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }

  // CRUD operations
  function addVariable(variable: Variable): void {
    variables.value.push(variable)
  }

  function isMultiSelectVariable(variable: Variable): variable is MultiSelectVariable {
  return variable.type === 'multi-select';
  }

   function isRangeVariable(variable: Variable): variable is RangeVariable {
  return variable.type === 'range';
  }

  function isPredicateVariable(variable: Variable): variable is PredicateVariable {
  return variable.type === 'predicate';
  }

  function updateVariable(id: string, updates: Partial<Variable>): boolean {
    const index = variables.value.findIndex(v => v.id === id)
    if (index === -1) return false
    const { type, ...rest } = updates;

    let currentVariable:any = variables.value[index];
    //TODO: fix type issues with variavles
    if(isMultiSelectVariable(currentVariable)){
      currentVariable = {
        ...currentVariable,
        ...rest,
        type: 'multi-select',
        updatedAt: new Date().toISOString()
      }
    } if (isPredicateVariable(currentVariable)){
      currentVariable = {
        ...currentVariable,
        ...rest,
        type: 'predicate',
        updatedAt: new Date().toISOString()
      }
    }if(isRangeVariable(currentVariable)){
      currentVariable = {
        ...currentVariable,
        ...rest,
        type: 'range',
        updatedAt: new Date().toISOString()
      }
    } else {
      // not multi select then 
      currentVariable = {
      ...currentVariable,
      ...rest,
      type: currentVariable.type,
      updatedAt: new Date().toISOString()
    }
    }
    variables.value[index] =  currentVariable;
    return true
  }

  function deleteVariable(id: string): boolean {
    const index = variables.value.findIndex(v => v.id === id)
    if (index === -1) return false

    variables.value.splice(index, 1)
    if (selectedVariableId.value === id) {
      selectedVariableId.value = null
    }
    return true
  }

  function getVariable(id: string): Variable | undefined {
    return allVariables.value.find(v => v.id === id)
  }

  function getVariableByName(name: string): Variable | undefined {
    return allVariables.value.find(v => v.name === name)
  }

  // Validation
  function validateVariableName(name: string, excludeId?: string): string[] {
    const errors: string[] = []
    
    if (!name.trim()) {
      errors.push('Variable name is required')
    }
    
    if (name.length < 2) {
      errors.push('Variable name must be at least 2 characters')
    }
    
    if (!/^[a-zA-Z][a-zA-Z0-9_\s]*$/.test(name)) {
      errors.push('Variable name must start with a letter and contain only letters, numbers, underscores, and spaces')
    }
    
    const existingVariable = getVariableByName(name)
    if (existingVariable && existingVariable.id !== excludeId) {
      errors.push('A variable with this name already exists')
    }
    
    return errors
  }

  function validateVariable(variable: Variable): string[] {
    const errors: string[] = []
    
    errors.push(...validateVariableName(variable.name, variable.id))
    
    if (variable.type === 'multi-select') {
      const multiSelect = variable as MultiSelectVariable
      if (multiSelect.options.length === 0) {
        errors.push('Multi-select variable must have at least one option')
      }
    }
    
    if (variable.type === 'range') {
      const range = variable as RangeVariable
      if (range.min >= range.max) {
        errors.push('Range minimum must be less than maximum')
      }
    }
    
    return errors
  }

  // Utility functions
  function generateId(): string {
    return `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  function getDefaultValueForType(type: VariableType): any {
    switch (type) {
      case 'number':
      case 'currency':
      case 'percentage':
        return 0
      case 'text':
        return ''
      case 'boolean':
        return false
      case 'multi-select':
        return []
      case 'range':
        return 0
      case 'predicate':
        return null
      default:
        return null
    }
  }

  function clearVariables(): void {
    variables.value = []
    selectedVariableId.value = null
  }

  function selectVariable(id: string): void {
    selectedVariableId.value = id
  }

  function deselectVariable(): void {
    selectedVariableId.value = null
  }

  return {
    // State
    variables: readonly(variables),
    selectedVariableId: readonly(selectedVariableId),
    
    // Computed
    userVariables,
    reservedVariables,
    allVariables,
    selectedVariable,
    variableOptions,
    userVariableOptions,
    
    // Creation functions
    createSimpleVariable,
    createMultiSelectVariable,
    createRangeVariable,
    createPredicateVariable,
    
    // CRUD operations
    addVariable,
    updateVariable,
    deleteVariable,
    getVariable,
    getVariableByName,
    
    // Validation
    validateVariableName,
    validateVariable,
    
    // Utility
    clearVariables,
    selectVariable,
    deselectVariable
  }
}
