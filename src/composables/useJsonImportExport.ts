import { ref } from 'vue'
import type { Expression, ExpressionExport, ExpressionSchema } from '../types'

export function useJsonImportExport() {
  const isExporting = ref(false)
  const isImporting = ref(false)
  const exportError = ref<string | null>(null)
  const importError = ref<string | null>(null)

  const currentSchema: ExpressionSchema = {
    version: '1.0',
    variableTypes: ['number', 'text', 'percentage', 'currency', 'boolean', 'multi-select', 'range', 'predicate'],
    operatorTypes: ['equals', 'not_equals', 'greater_than', 'less_than', 'greater_equal', 'less_equal', 'is_empty', 'is_not_empty', 'contains', 'not_contains', 'in', 'not_in'],
    actionTypes: ['assignment', 'calculation', 'value']
  }

  // Export functions
  function exportToJson(expression: Expression, format: 'full' | 'minimal' = 'full'): ExpressionExport {
    const exportData: ExpressionExport = {
      version: '1.0',
      metadata: {
        ...expression.metadata,
        exportedAt: new Date().toISOString(),
        exportFormat: format
      },
      expression: {
        ...expression,
        // Clean up any runtime-only properties
        variables: expression.variables.map(variable => ({
          ...variable,
          // Remove any UI-specific properties that shouldn't be serialized
        })),
        conditions: expression.conditions.map(condition => ({
          ...condition,
          // Remove any UI-specific properties that shouldn't be serialized
        }))
      }
    }

    if (format === 'full') {
      exportData.schema = currentSchema
    }

    return exportData
  }

  function exportToJsonString(expression: Expression, format: 'full' | 'minimal' = 'full', pretty = true): string {
    try {
      isExporting.value = true
      exportError.value = null

      const exportData = exportToJson(expression, format)
      const jsonString = pretty ? JSON.stringify(exportData, null, 2) : JSON.stringify(exportData)
      
      return jsonString
    } catch (error) {
      exportError.value = error instanceof Error ? error.message : 'Unknown export error'
      throw error
    } finally {
      isExporting.value = false
    }
  }

  function downloadAsJson(expression: Expression, filename?: string, format: 'full' | 'minimal' = 'full'): void {
    try {
      const jsonString = exportToJsonString(expression, format, true)
      const blob = new Blob([jsonString], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = filename || `${expression.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${Date.now()}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)
    } catch (error) {
      exportError.value = error instanceof Error ? error.message : 'Failed to download JSON'
      throw error
    }
  }

  // Import functions
  function validateJsonStructure(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check required top-level properties
    if (!data.version) {
      errors.push('Missing version field')
    }

    if (!data.expression) {
      errors.push('Missing expression field')
    }

    if (!data.metadata) {
      errors.push('Missing metadata field')
    }

    // Validate expression structure
    if (data.expression) {
      const expr = data.expression
      
      if (!expr.id) errors.push('Expression missing id')
      if (!expr.name) errors.push('Expression missing name')
      if (!Array.isArray(expr.variables)) errors.push('Expression variables must be an array')
      if (!Array.isArray(expr.conditions)) errors.push('Expression conditions must be an array')
      if (!expr.metadata) errors.push('Expression missing metadata')

      // Validate variables
      if (Array.isArray(expr.variables)) {
        expr.variables.forEach((variable: any, index: number) => {
          if (!variable.id) errors.push(`Variable ${index + 1} missing id`)
          if (!variable.name) errors.push(`Variable ${index + 1} missing name`)
          if (!variable.type) errors.push(`Variable ${index + 1} missing type`)
          if (!currentSchema.variableTypes.includes(variable.type)) {
            errors.push(`Variable ${index + 1} has invalid type: ${variable.type}`)
          }
        })
      }

      // Validate conditions
      if (Array.isArray(expr.conditions)) {
        expr.conditions.forEach((condition: any, index: number) => {
          if (!condition.id) errors.push(`Condition ${index + 1} missing id`)
          if (!condition.variable) errors.push(`Condition ${index + 1} missing variable`)
          if (!condition.operator) errors.push(`Condition ${index + 1} missing operator`)
          if (!currentSchema.operatorTypes.includes(condition.operator)) {
            errors.push(`Condition ${index + 1} has invalid operator: ${condition.operator}`)
          }
          if (!condition.thenAction) errors.push(`Condition ${index + 1} missing thenAction`)
          
          // Validate actions
          if (condition.thenAction && !currentSchema.actionTypes.includes(condition.thenAction.type)) {
            errors.push(`Condition ${index + 1} thenAction has invalid type: ${condition.thenAction.type}`)
          }
          if (condition.elseAction && !currentSchema.actionTypes.includes(condition.elseAction.type)) {
            errors.push(`Condition ${index + 1} elseAction has invalid type: ${condition.elseAction.type}`)
          }
        })
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  function importFromJson(jsonData: ExpressionExport): Expression {
    try {
      isImporting.value = true
      importError.value = null

      // Validate structure
      const validation = validateJsonStructure(jsonData)
      if (!validation.isValid) {
        throw new Error(`Invalid JSON structure: ${validation.errors.join(', ')}`)
      }

      // Check version compatibility
      if (jsonData.version !== '1.0') {
        console.warn(`Importing from version ${jsonData.version}, current version is 1.0`)
      }

      // Return the expression with updated metadata
      const importedExpression: Expression = {
        ...jsonData.expression,
        metadata: {
          ...jsonData.expression.metadata,
          updatedAt: new Date().toISOString()
        }
      }

      return importedExpression
    } catch (error) {
      importError.value = error instanceof Error ? error.message : 'Unknown import error'
      throw error
    } finally {
      isImporting.value = false
    }
  }

  function importFromJsonString(jsonString: string): Expression {
    try {
      const jsonData = JSON.parse(jsonString)
      return importFromJson(jsonData)
    } catch (error) {
      if (error instanceof SyntaxError) {
        importError.value = 'Invalid JSON format'
        throw new Error('Invalid JSON format')
      }
      throw error
    }
  }

  function importFromFile(file: File): Promise<Expression> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (event) => {
        try {
          const jsonString = event.target?.result as string
          const expression = importFromJsonString(jsonString)
          resolve(expression)
        } catch (error) {
          reject(error)
        }
      }
      
      reader.onerror = () => {
        const error = new Error('Failed to read file')
        importError.value = error.message
        reject(error)
      }
      
      reader.readAsText(file)
    })
  }

  // Utility functions
  function copyToClipboard(expression: Expression, format: 'full' | 'minimal' = 'minimal'): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const jsonString = exportToJsonString(expression, format, true)
        
        if (navigator.clipboard && window.isSecureContext) {
          navigator.clipboard.writeText(jsonString).then(resolve).catch(reject)
        } else {
          // Fallback for older browsers
          const textArea = document.createElement('textarea')
          textArea.value = jsonString
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          
          try {
            document.execCommand('copy')
            resolve()
          } catch (err) {
            reject(err)
          } finally {
            document.body.removeChild(textArea)
          }
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  function clearErrors(): void {
    exportError.value = null
    importError.value = null
  }

  function getSchemaInfo(): ExpressionSchema {
    return { ...currentSchema }
  }

  return {
    // State
    isExporting,
    isImporting,
    exportError,
    importError,

    // Export functions
    exportToJson,
    exportToJsonString,
    downloadAsJson,

    // Import functions
    importFromJson,
    importFromJsonString,
    importFromFile,
    validateJsonStructure,

    // Utility functions
    copyToClipboard,
    clearErrors,
    getSchemaInfo
  }
}
