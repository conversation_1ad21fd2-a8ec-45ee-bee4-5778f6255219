/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BaseButton: typeof import('./components/ui/BaseButton.vue')['default']
    BaseInput: typeof import('./components/ui/BaseInput.vue')['default']
    BaseModal: typeof import('./components/ui/BaseModal.vue')['default']
    BaseSelect: typeof import('./components/ui/BaseSelect.vue')['default']
    ConditionBuilder: typeof import('./components/ConditionBuilder.vue')['default']
    ExpressionDisplay: typeof import('./components/ExpressionDisplay.vue')['default']
    ExpressionEditor: typeof import('./components/ExpressionEditor.vue')['default']
    ImportExportModal: typeof import('./components/ImportExportModal.vue')['default']
    MultiSelectVariable: typeof import('./components/variables/MultiSelectVariable.vue')['default']
    PredicateVariable: typeof import('./components/variables/PredicateVariable.vue')['default']
    RangeVariable: typeof import('./components/variables/RangeVariable.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VariableItem: typeof import('./components/variables/VariableItem.vue')['default']
    VariableManager: typeof import('./components/VariableManager.vue')['default']
    VariableModal: typeof import('./components/variables/VariableModal.vue')['default']
    VariableValueDisplay: typeof import('./components/variables/VariableValueDisplay.vue')['default']
  }
}
