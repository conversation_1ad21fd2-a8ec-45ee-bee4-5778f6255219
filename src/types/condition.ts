import type { ComparisonOperator, Action, LogicalExpression } from './variable'

export interface Condition {
  id: string
  variable: string
  operator: ComparisonOperator
  value: any
  thenAction: Action
  elseAction?: Action
  createdAt?: string
  updatedAt?: string
}

export interface ConditionGroup {
  id: string
  type: 'IF' | 'ELSE_IF' | 'ELSE'
  conditions: Condition[]
  logicType: 'AND' | 'OR'
  nestedGroups?: ConditionGroup[]
}

export interface ConditionBuilder {
  groups: ConditionGroup[]
  variables: string[] // Available variable IDs
  operators: ComparisonOperator[]
}

// Mathematical operators for calculations
export type MathOperator = '+' | '-' | '*' | '/' | '%' | '^'

export interface CalculationExpression {
  left: string | number // Variable ID or literal value
  operator: MathOperator
  right: string | number // Variable ID or literal value
}

// For building complex nested conditions
export interface NestedCondition {
  id: string
  parentId?: string
  level: number
  expression: LogicalExpression
  thenAction: Action
  elseAction?: Action | NestedCondition
}

export interface ConditionValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Condition templates for common patterns
export interface ConditionTemplate {
  id: string
  name: string
  description: string
  template: Condition
  category: 'comparison' | 'range' | 'logic' | 'calculation'
}
