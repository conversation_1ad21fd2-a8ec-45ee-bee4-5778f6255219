import type { Variable } from './variable'
import type { Condition, ConditionGroup } from './condition'

export interface Expression {
  id: string
  name: string
  description?: string
  variables: Variable[]
  conditions: Condition[]
  conditionGroups?: ConditionGroup[]
  generatedExpression: string
  metadata: ExpressionMetadata
  version: string
}

export interface ExpressionMetadata {
  createdAt: string
  updatedAt: string
  author?: string
  tags?: string[]
  category?: string
  isTemplate?: boolean
}

export interface ExpressionValidation {
  isValid: boolean
  errors: ExpressionError[]
  warnings: ExpressionWarning[]
  circularDependencies: string[]
  unreferencedVariables: string[]
}

export interface ExpressionError {
  type: 'syntax' | 'reference' | 'type' | 'logic'
  message: string
  location?: {
    variableId?: string
    conditionId?: string
    line?: number
    column?: number
  }
}

export interface ExpressionWarning {
  type: 'unused' | 'deprecated' | 'performance'
  message: string
  location?: {
    variableId?: string
    conditionId?: string
  }
}

// For JSON import/export
export interface ExpressionExport {
  version: string
  metadata: ExpressionMetadata & {
    exportedAt: string
    exportFormat: 'full' | 'minimal'
  }
  expression: Expression
  schema?: ExpressionSchema
}

export interface ExpressionSchema {
  version: string
  variableTypes: string[]
  operatorTypes: string[]
  actionTypes: string[]
}

// For expression parsing and generation
export interface ExpressionNode {
  type: 'condition' | 'variable' | 'operator' | 'value' | 'group'
  value: any
  children?: ExpressionNode[]
  parent?: ExpressionNode
}

export interface ExpressionTree {
  root: ExpressionNode
  variables: Map<string, Variable>
  conditions: Map<string, Condition>
}

// Expression execution context
export interface ExpressionContext {
  variables: Record<string, any>
  functions: Record<string, Function>
  constants: Record<string, any>
}

export interface ExpressionResult {
  success: boolean
  value?: any
  error?: string
  executionTime?: number
  variablesUsed: string[]
}
