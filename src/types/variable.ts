export type VariableType = 'number' | 'text' | 'percentage' | 'currency' | 'boolean' | 'multi-select' | 'range' | 'predicate'

export type RangeOperator = 'between' | 'greater_than' | 'less_than' | 'greater_equal' | 'less_equal' | 'equals'

export type LogicType = 'AND' | 'OR'

export interface SelectOption {
  value: string | number
  label: string
  description?: string
}

export interface BaseVariable {
  id: string
  name: string
  type: VariableType
  description?: string
  defaultValue?: any
  metadata?: Record<string, any>
  createdAt?: string
  updatedAt?: string
}

export interface SimpleVariable extends BaseVariable {
  type: 'number' | 'text' | 'percentage' | 'currency' | 'boolean'
  value: string | number | boolean
}

export interface MultiSelectVariable extends BaseVariable {
  type: 'multi-select'
  options: SelectOption[]
  logicType: LogicType
  selectedValues: string[]
  minSelections?: number
  maxSelections?: number
}

export interface RangeVariable extends BaseVariable {
  type: 'range'
  min: number
  max: number
  step?: number
  operator: RangeOperator
  value: number | [number, number]
  tiers?: RangeTier[]
}

export interface RangeTier {
  id: string
  name: string
  min: number
  max: number
  value: any
}

export interface PredicateVariable extends BaseVariable {
  type: 'predicate'
  conditions: ConditionalExpression[]
  defaultValue: any
  dependencies: string[] // IDs of variables this depends on
}

export interface ConditionalExpression {
  id: string
  if: LogicalExpression
  then: Action
  else?: Action | ConditionalExpression
}

export interface LogicalExpression {
  variable: string
  operator: ComparisonOperator
  value: any
  logicType?: LogicType // For combining multiple expressions
  expressions?: LogicalExpression[] // For nested expressions
}

export interface Action {
  type: 'assignment' | 'calculation' | 'value'
  target?: string // Variable ID for assignment
  expression?: string // Mathematical expression
  value?: any // Direct value assignment
}

export type ComparisonOperator = 
  | 'equals' 
  | 'not_equals' 
  | 'greater_than' 
  | 'less_than' 
  | 'greater_equal' 
  | 'less_equal' 
  | 'is_empty' 
  | 'is_not_empty'
  | 'contains'
  | 'not_contains'
  | 'in'
  | 'not_in'

export type Variable = SimpleVariable | MultiSelectVariable | RangeVariable | PredicateVariable

// Reserved system variables
export interface ReservedVariable {
  name: string
  value: string
  description: string
  type: VariableType
}

export const RESERVED_VARIABLES: ReservedVariable[] = [
  { name: 'Total Sales', value: 'total_sales', description: 'Total sales amount for the period', type: 'currency' },
  { name: 'PAR', value: 'par', description: 'Portfolio at Risk percentage', type: 'percentage' },
  { name: 'Portfolio Size', value: 'portfolio_size', description: 'Total portfolio size', type: 'currency' },
  { name: 'Number of New Loans', value: 'number_of_new_loans', description: 'Count of new loans issued', type: 'number' },
  { name: 'Number of Repeat Loans', value: 'number_of_repeat_loans', description: 'Count of repeat loans issued', type: 'number' },
  { name: 'Loan Products', value: 'loan_products', description: 'Number of different loan products', type: 'number' }
]
