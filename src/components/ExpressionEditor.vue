<template>
  <div class="expression-editor">
    <!-- Header -->
    <div class="bg-primary-500 text-white">
      <div class="container py-4">
        <div class="flex items-center justify-between">
          <h1 class="text-xl font-semibold">Expression Editor</h1>
          <div class="flex items-center space-x-3">
            <BaseButton
              variant="secondary"
              size="sm"
              @click="showImportModal = true"
            >
              Import
            </BaseButton>
            <BaseButton
              variant="secondary"
              size="sm"
              @click="showExportModal = true"
            >
              Export
            </BaseButton>
            <BaseButton
              variant="primary"
              @click="handleSave"
              :loading="isSaving"
            >
              Save Expression
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="container py-6">
      <div class="space-y-6">
        <!-- Expression Name -->
        <div class="card p-6">
          <div class="form-group">
            <label class="form-label">Expression Name</label>
            <BaseInput
              :model-value="expressionBuilder.expression.value.name"
              placeholder="Enter expression name"
              @update:model-value="expressionBuilder.updateExpressionName"
            />
          </div>
          
          <div class="form-group mt-4">
            <label class="form-label">Description</label>
            <BaseInput
              :model-value="expressionBuilder.expression.value.description || ''"
              placeholder="Enter expression description"
              @update:model-value="expressionBuilder.updateExpressionDescription"
            />
          </div>
        </div>

        <!-- Test Scenarios Section -->
        <div class="card">
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Quick Start</h2>
          </div>

          <div class="p-6">
            <TestScenarios @implement-scenario="handleImplementScenario" />
          </div>
        </div>

        <!-- Variables Section -->
        <div class="card">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold text-gray-900">Variables</h2>
              <BaseButton
                variant="primary"
                @click="showVariableModal = true"
              >
                + Add Variable
              </BaseButton>
            </div>
          </div>
          
          <div class="p-6">
            <VariableManager
              :variables="expressionBuilder.variableManager.variables.value"
              @edit="handleEditVariable"
              @duplicate="handleDuplicateVariable"
              @delete="handleDeleteVariable"
            />
          </div>
        </div>

        <!-- Condition Sets Section -->
        <div class="card">
          <div class="p-6">
            <ConditionSetBuilder
              :condition-sets="expressionBuilder.conditionBuilder.conditionSets.value"
              :variables="expressionBuilder.variableManager.allVariables.value"
              @add-condition-set="handleAddConditionSet"
              @remove-condition-set="handleRemoveConditionSet"
              @update-condition-set="handleUpdateConditionSet"
              @add-condition-to-set="handleAddConditionToSet"
              @remove-condition-from-set="handleRemoveConditionFromSet"
              @update-condition-in-set="handleUpdateConditionInSet"
              @update-condition-action-in-set="handleUpdateConditionActionInSet"
            />
          </div>
        </div>

        <!-- Individual Conditions Section -->
        <div class="card">
          <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold text-gray-900">Individual Conditions</h2>
              <BaseButton
                variant="primary"
                @click="handleAddCondition"
              >
                + Add Condition
              </BaseButton>
            </div>
          </div>

          <div class="p-6">
            <ConditionBuilder
              :conditions="expressionBuilder.conditionBuilder.conditions.value"
              :variables="expressionBuilder.variableManager.allVariables.value"
              @update="handleConditionUpdate"
              @update-then-action="handleThenActionUpdate"
              @update-else-action="handleElseActionUpdate"
              @update-then-expression="handleThenExpressionUpdate"
              @add-else-action="handleAddElseAction"
              @add="handleAddCondition"
              @remove="handleRemoveCondition"
            />
          </div>
        </div>

        <!-- Expression Display -->
        <div class="card p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Generated Expression</h2>
          <ExpressionDisplay
            :expression="expressionBuilder.generatedExpression.value"
            :is-valid="expressionBuilder.isValid.value"
            :errors="expressionBuilder.validationErrors.value"
            :warnings="expressionBuilder.validationWarnings.value"
          />
        </div>
      </div>
    </div>

    <!-- Modals -->
    <VariableModal
      v-model="showVariableModal"
      :variable="editingVariable"
      @save="handleSaveVariable"
      @cancel="handleCancelVariable"
    />

    <ImportExportModal
      v-model="showImportModal"
      mode="import"
      @import="handleImport"
    />

    <ImportExportModal
      v-model="showExportModal"
      mode="export"
      :expression="expressionBuilder.expression.value"
      @export="handleExport"
    />

    <!-- Notifications -->
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="[
          'px-4 py-3 rounded-md shadow-lg transition-all duration-300',
          getNotificationClasses(notification.type)
        ]"
      >
        <div class="flex items-center">
          <span class="mr-2">{{ getNotificationIcon(notification.type) }}</span>
          <span class="text-sm font-medium">{{ notification.message }}</span>
          <button
            @click="removeNotification(notification.id)"
            class="ml-3 text-white hover:text-gray-200"
          >
            ×
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useExpressionBuilder } from '@/composables/useExpressionBuilder'
import { useJsonImportExport } from '@/composables/useJsonImportExport'
import type { Variable, Expression, NotificationMessage } from '@/types'

import BaseButton from '@/components/ui/BaseButton.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import VariableManager from '@/components/VariableManager.vue'
import ConditionBuilder from '@/components/ConditionBuilder.vue'
import ConditionSetBuilder from '@/components/ConditionSetBuilder.vue'
import ExpressionDisplay from '@/components/ExpressionDisplay.vue'
import VariableModal from '@/components/variables/VariableModal.vue'
import ImportExportModal from '@/components/ImportExportModal.vue'
import TestScenarios from '@/components/TestScenarios.vue'

// Composables
const expressionBuilder = useExpressionBuilder()
const jsonImportExport = useJsonImportExport()

// Modal states
const showVariableModal = ref(false)
const showImportModal = ref(false)
const showExportModal = ref(false)

// Variable editing
const editingVariable = ref<Variable | null>(null)

// UI state
const isSaving = ref(false)
const notifications = ref<NotificationMessage[]>([])

// Variable management
function handleEditVariable(variable: Variable) {
  editingVariable.value = variable
  showVariableModal.value = true
}

function handleDuplicateVariable(variable: Variable) {
  const duplicated = {
    ...variable,
    id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: `${variable.name} (Copy)`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  expressionBuilder.variableManager.addVariable(duplicated)
  showNotification('Variable duplicated successfully', 'success')
}

function handleDeleteVariable(variable: Variable) {
  expressionBuilder.variableManager.deleteVariable(variable.id)
  showNotification('Variable deleted', 'info')
}

function handleSaveVariable(variable: Variable) {
  if (editingVariable.value) {
    // Update existing variable
    expressionBuilder.variableManager.updateVariable(variable.id, variable)
    showNotification('Variable updated successfully', 'success')
  } else {
    // Add new variable
    expressionBuilder.variableManager.addVariable(variable)
    showNotification('Variable added successfully', 'success')
  }
  
  editingVariable.value = null
  showVariableModal.value = false
}

function handleCancelVariable() {
  editingVariable.value = null
  showVariableModal.value = false
}

// Condition management
function handleAddCondition() {
  const newCondition = expressionBuilder.conditionBuilder.createCondition(
    '', // variable - to be selected by user
    'equals', // default operator
    '', // value - to be set by user
    expressionBuilder.conditionBuilder.createAction('value', undefined, undefined, 0) // default then action
  )
  
  expressionBuilder.conditionBuilder.addCondition(newCondition)
  showNotification('New condition added', 'success')
}

function handleConditionUpdate(conditionId: string, field: keyof any, value: any) {
  const success = expressionBuilder.conditionBuilder.updateCondition(conditionId, { [field]: value })
  if (success) {
    showNotification('Condition updated', 'success')
  } else {
    showNotification('Failed to update condition', 'error')
  }
}

function handleThenActionUpdate(conditionId: string, field: string, value: any) {
  const condition = expressionBuilder.conditionBuilder.getCondition(conditionId)
  if (condition && condition.thenAction) {
    const updatedThenAction = { ...condition.thenAction, [field]: value }
    const success = expressionBuilder.conditionBuilder.updateCondition(conditionId, { thenAction: updatedThenAction })
    if (success) {
      showNotification('Then action updated', 'success')
    }
  }
}

function handleElseActionUpdate(conditionId: string, field: string, value: any) {
  const condition = expressionBuilder.conditionBuilder.getCondition(conditionId)
  if (condition && condition.elseAction) {
    const updatedElseAction = { ...condition.elseAction, [field]: value }
    const success = expressionBuilder.conditionBuilder.updateCondition(conditionId, { elseAction: updatedElseAction })
    if (success) {
      showNotification('Else action updated', 'success')
    }
  }
}

function handleThenExpressionUpdate(conditionId: string, part: 'source' | 'operator' | 'target', value: any) {
  const condition = expressionBuilder.conditionBuilder.getCondition(conditionId)
  if (condition && condition.thenAction) {
    // Handle calculation expression updates
    let expression = condition.thenAction.expression || ''

    // Parse and update the expression based on the part
    if (part === 'source') {
      // Update source variable in expression
      const variable = expressionBuilder.variableManager.getVariable(value)
      const variableName = variable?.name || value
      expression = expression.replace(/^[^+\-*/]*/, variableName)
    } else if (part === 'operator') {
      // Update operator in expression
      expression = expression.replace(/[+\-*/]/, value)
    } else if (part === 'target') {
      // Update target variable in expression
      const variable = expressionBuilder.variableManager.getVariable(value)
      const variableName = variable?.name || value
      expression = expression.replace(/[+\-*/][^+\-*/]*$/, `${expression.match(/[+\-*/]/)?.[0] || '*'}${variableName}`)
    }

    const updatedThenAction = { ...condition.thenAction, expression }
    const success = expressionBuilder.conditionBuilder.updateCondition(conditionId, { thenAction: updatedThenAction })
    if (success) {
      showNotification('Expression updated', 'success')
    }
  }
}

function handleAddElseAction(conditionId: string) {
  const condition = expressionBuilder.conditionBuilder.getCondition(conditionId)
  if (condition && !condition.elseAction) {
    const elseAction = expressionBuilder.conditionBuilder.createAction('value', undefined, undefined, 0)
    const success = expressionBuilder.conditionBuilder.updateCondition(conditionId, { elseAction })
    if (success) {
      showNotification('Else action added', 'success')
    }
  }
}

function handleRemoveCondition(conditionId: string) {
  const success = expressionBuilder.conditionBuilder.deleteCondition(conditionId)
  if (success) {
    showNotification('Condition removed', 'success')
  } else {
    showNotification('Failed to remove condition', 'error')
  }
}

// Condition Set Management
function handleAddConditionSet() {
  const newConditionSet = expressionBuilder.conditionBuilder.createConditionSet(
    `Condition Set ${expressionBuilder.conditionBuilder.conditionSets.value.length + 1}`,
    'New condition set',
    false,
    expressionBuilder.conditionBuilder.conditionSets.value.length + 1
  )

  expressionBuilder.conditionBuilder.addConditionSet(newConditionSet)
  showNotification('Condition set added', 'success')
}

function handleRemoveConditionSet(setId: string) {
  const success = expressionBuilder.conditionBuilder.deleteConditionSet(setId)
  if (success) {
    showNotification('Condition set removed', 'success')
  }
}

function handleUpdateConditionSet(setId: string, field: string, value: any) {
  const success = expressionBuilder.conditionBuilder.updateConditionSet(setId, { [field]: value })
  if (success) {
    showNotification('Condition set updated', 'success')
  }
}

function handleAddConditionToSet(setId: string) {
  const newCondition = expressionBuilder.conditionBuilder.createCondition(
    '', // variable - to be selected by user
    'equals', // default operator
    '', // value - to be set by user
    expressionBuilder.conditionBuilder.createAction('value', undefined, undefined, 0) // default then action
  )

  const success = expressionBuilder.conditionBuilder.addConditionToSet(setId, newCondition)
  if (success) {
    showNotification('Condition added to set', 'success')
  }
}

function handleRemoveConditionFromSet(setId: string, conditionId: string) {
  const success = expressionBuilder.conditionBuilder.removeConditionFromSet(setId, conditionId)
  if (success) {
    showNotification('Condition removed from set', 'success')
  }
}

function handleUpdateConditionInSet(setId: string, conditionId: string, field: string, value: any) {
  // Find the condition set and update the specific condition
  const conditionSet = expressionBuilder.conditionBuilder.conditionSets.value.find(cs => cs.id === setId)
  if (conditionSet) {
    const condition = conditionSet.conditions.find(c => c.id === conditionId)
    if (condition) {
      const updatedCondition = { ...condition, [field]: value }
      const conditionIndex = conditionSet.conditions.findIndex(c => c.id === conditionId)
      conditionSet.conditions[conditionIndex] = updatedCondition
      showNotification('Condition updated', 'success')
    }
  }
}

function handleUpdateConditionActionInSet(setId: string, conditionId: string, actionType: string, field: string, value: any) {
  // Find the condition set and update the specific condition action
  const conditionSet = expressionBuilder.conditionBuilder.conditionSets.value.find(cs => cs.id === setId)
  if (conditionSet) {
    const condition = conditionSet.conditions.find(c => c.id === conditionId)
    if (condition) {
      const updatedAction = { ...condition[actionType as keyof typeof condition], [field]: value }
      const updatedCondition = { ...condition, [actionType]: updatedAction }
      const conditionIndex = conditionSet.conditions.findIndex(c => c.id === conditionId)
      conditionSet.conditions[conditionIndex] = updatedCondition
      showNotification('Condition action updated', 'success')
    }
  }
}

// Save/Load
async function handleSave() {
  try {
    isSaving.value = true
    
    // Validate expression
    if (!expressionBuilder.isValid.value) {
      showNotification('Please fix validation errors before saving', 'error')
      return
    }
    
    expressionBuilder.saveExpression()
    showNotification('Expression saved successfully', 'success')
  } catch (error) {
    showNotification('Failed to save expression', 'error')
  } finally {
    isSaving.value = false
  }
}

function handleImport(expression: Expression) {
  try {
    expressionBuilder.loadExpression(expression)
    showImportModal.value = false
    showNotification('Expression imported successfully', 'success')
  } catch (error) {
    showNotification('Failed to import expression', 'error')
  }
}

function handleExport() {
  showExportModal.value = false
  showNotification('Expression exported successfully', 'success')
}

// Test Scenario Implementation
function handleImplementScenario(scenario: any) {
  try {
    // Clear existing data
    expressionBuilder.resetExpression()

    // Update expression metadata
    expressionBuilder.updateExpressionName(scenario.name)
    expressionBuilder.updateExpressionDescription(scenario.description)

    // Create variables
    scenario.variables.forEach((varDef: any) => {
      const variable = createVariableFromDefinition(varDef)
      expressionBuilder.variableManager.addVariable(variable)
    })

    // Implement specific scenario logic
    if (scenario.id === 'end-month-commission') {
      implementEndMonthCommissionScenario()
    } else if (scenario.id === 'weekly-loan-incentive') {
      implementWeeklyLoanIncentiveScenario()
    } else if (scenario.id === 'portfolio-commission') {
      implementPortfolioCommissionScenario()
    }

    showNotification(`${scenario.name} scenario implemented successfully!`, 'success')
  } catch (error) {
    console.error('Failed to implement scenario:', error)
    showNotification('Failed to implement scenario', 'error')
  }
}

function implementEndMonthCommissionScenario() {
  // Find the variables we need
  const inclusiveRateVar = expressionBuilder.variableManager.variables.value.find(v => v.name === 'Inclusive Rate')
  const newLoanRateVar = expressionBuilder.variableManager.variables.value.find(v => v.name === 'New Loan Rate')
  const repeatLoanRateVar = expressionBuilder.variableManager.variables.value.find(v => v.name === 'Repeat Loan Rate')
  const finalCommissionVar = expressionBuilder.variableManager.variables.value.find(v => v.name === 'Final Commission')

  if (!inclusiveRateVar || !newLoanRateVar || !repeatLoanRateVar || !finalCommissionVar) {
    throw new Error('Required variables not found')
  }

  // Create the priority-based condition chain
  // IF(Inclusive Rate is not empty) THEN Final Commission = Total Sales × Inclusive Rate
  const condition1 = expressionBuilder.conditionBuilder.createCondition(
    inclusiveRateVar.id,
    'is_not_empty',
    null,
    expressionBuilder.conditionBuilder.createAction('calculation', finalCommissionVar.id, 'total_sales * inclusive_rate', undefined)
  )

  // ELSE IF(New Loan Rate is not empty) THEN Final Commission = Total Sales × New Loan Rate
  const elseAction1 = expressionBuilder.conditionBuilder.createCondition(
    newLoanRateVar.id,
    'is_not_empty',
    null,
    expressionBuilder.conditionBuilder.createAction('calculation', finalCommissionVar.id, 'total_sales * new_loan_rate', undefined)
  )

  // ELSE IF(Repeat Loan Rate is not empty) THEN Final Commission = Total Sales × Repeat Loan Rate
  const elseAction2 = expressionBuilder.conditionBuilder.createCondition(
    repeatLoanRateVar.id,
    'is_not_empty',
    null,
    expressionBuilder.conditionBuilder.createAction('calculation', finalCommissionVar.id, 'total_sales * repeat_loan_rate', undefined)
  )

  // ELSE Final Commission = 0
  const finalElseAction = expressionBuilder.conditionBuilder.createAction('assignment', finalCommissionVar.id, undefined, 0)

  // Chain the conditions together
  elseAction2.elseAction = finalElseAction
  elseAction1.elseAction = elseAction2
  condition1.elseAction = elseAction1

  expressionBuilder.conditionBuilder.addCondition(condition1)
}

function implementWeeklyLoanIncentiveScenario() {
  // This scenario has three independent condition sets
  // Create condition sets for the complex multi-condition logic

  // Condition Set 1: Tiered Fixed Amount
  const conditionSet1 = expressionBuilder.conditionBuilder.createConditionSet(
    'Tiered Fixed Amount',
    'Fixed amount based on sales tiers',
    true, // independent
    1 // priority
  )

  // Condition Set 2: Target-Based Rate
  const conditionSet2 = expressionBuilder.conditionBuilder.createConditionSet(
    'Target-Based Rate',
    'Rate based on meeting weekly target',
    true, // independent
    2 // priority
  )

  // Condition Set 3: Loan Type Rates
  const conditionSet3 = expressionBuilder.conditionBuilder.createConditionSet(
    'Loan Type Rates',
    'Different rates for different loan types',
    true, // independent
    3 // priority
  )

  expressionBuilder.conditionBuilder.addConditionSet(conditionSet1)
  expressionBuilder.conditionBuilder.addConditionSet(conditionSet2)
  expressionBuilder.conditionBuilder.addConditionSet(conditionSet3)

  // Add basic conditions to each set (simplified for now)
  const basicCondition1 = expressionBuilder.conditionBuilder.createCondition(
    'total_sales',
    'greater_than',
    0,
    expressionBuilder.conditionBuilder.createAction('value', undefined, undefined, 'Tiered calculation')
  )

  const basicCondition2 = expressionBuilder.conditionBuilder.createCondition(
    'total_sales',
    'greater_equal',
    'weekly_target',
    expressionBuilder.conditionBuilder.createAction('value', undefined, undefined, 'Target-based calculation')
  )

  const basicCondition3 = expressionBuilder.conditionBuilder.createCondition(
    'loan_type',
    'equals',
    'Buyoff',
    expressionBuilder.conditionBuilder.createAction('value', undefined, undefined, 'Loan type calculation')
  )

  expressionBuilder.conditionBuilder.addConditionToSet(conditionSet1.id, basicCondition1)
  expressionBuilder.conditionBuilder.addConditionToSet(conditionSet2.id, basicCondition2)
  expressionBuilder.conditionBuilder.addConditionToSet(conditionSet3.id, basicCondition3)
}

function implementPortfolioCommissionScenario() {
  // Find the variables we need
  const portfolioCategoryVar = expressionBuilder.variableManager.variables.value.find(v => v.name === 'Portfolio Category')
  const portfolioSizeVar = expressionBuilder.variableManager.variables.value.find(v => v.name === 'Portfolio Size')
  const finalCommissionVar = expressionBuilder.variableManager.variables.value.find(v => v.name === 'Final Commission')

  if (!portfolioCategoryVar || !portfolioSizeVar || !finalCommissionVar) {
    throw new Error('Required variables not found for portfolio commission')
  }

  // Create the category-based condition with range and PAR checks
  const condition1 = expressionBuilder.conditionBuilder.createCondition(
    portfolioCategoryVar.id,
    'equals',
    'Fixed Amount',
    expressionBuilder.conditionBuilder.createAction('assignment', finalCommissionVar.id, undefined, 3000)
  )

  // Add additional conditions for PAR and portfolio size range
  // This is simplified - in a full implementation, we'd create complex AND conditions

  expressionBuilder.conditionBuilder.addCondition(condition1)
}

function createVariableFromDefinition(varDef: any): Variable {
  const baseVariable = {
    id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: varDef.name,
    type: varDef.type as any,
    description: varDef.description,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  if (varDef.type === 'multi-select' && varDef.options) {
    return {
      ...baseVariable,
      type: 'multi-select',
      options: varDef.options,
      logicType: 'OR',
      selectedValues: [],
      minSelections: 1,
      maxSelections: 1
    } as any
  }

  // Simple variable types
  return {
    ...baseVariable,
    value: varDef.defaultValue || ''
  } as any
}

// Notifications
function showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
  const notification: NotificationMessage = {
    id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    message,
    duration: 5000
  }
  
  notifications.value.push(notification)
  
  // Auto-remove after duration
  setTimeout(() => {
    removeNotification(notification.id)
  }, notification.duration)
}

function removeNotification(id: string) {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

function getNotificationClasses(type: string): string {
  const classes = {
    success: 'bg-green-500 text-white',
    error: 'bg-red-500 text-white',
    warning: 'bg-yellow-500 text-white',
    info: 'bg-blue-500 text-white'
  }
  return classes[type as keyof typeof classes] || classes.info
}

function getNotificationIcon(type: string): string {
  const icons = {
    success: '✓',
    error: '✗',
    warning: '⚠',
    info: 'ℹ'
  }
  return icons[type as keyof typeof icons] || icons.info
}

// Initialize
onMounted(() => {
  // Any initialization logic
})
</script>
