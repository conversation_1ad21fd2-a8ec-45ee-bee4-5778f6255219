<template>
  <div class="condition-builder">
    <div v-if="conditions.length > 0" class="space-y-4">
      <div
        v-for="(condition, index) in conditions"
        :key="condition.id"
        class="condition-item bg-gray-50 border border-gray-200 rounded-lg p-4"
      >
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-sm font-medium text-gray-900">
            Condition {{ index + 1 }}
          </h3>
          <BaseButton
            variant="danger"
            size="sm"
            @click="removeCondition(condition.id)"
          >
            ×
          </BaseButton>
        </div>
        
        <!-- IF clause -->
        <div class="grid grid-cols-12 gap-2 items-center mb-3">
          <div class="col-span-1">
            <span class="text-sm font-medium text-primary-600">IF</span>
          </div>
          
          <div class="col-span-4">
            <BaseSelect
              :model-value="condition.variable"
              :options="variableOptions"
              placeholder="Select variable"
              size="sm"
              @update:model-value="updateCondition(condition.id, 'variable', $event)"
            />
          </div>
          
          <div class="col-span-3">
            <BaseSelect
              :model-value="condition.operator"
              :options="operatorOptions"
              placeholder="Operator"
              size="sm"
              @update:model-value="updateCondition(condition.id, 'operator', $event)"
            />
          </div>
          
          <div class="col-span-4">
            <!-- Range input for BETWEEN operators -->
            <div v-if="['between', 'not_between'].includes(condition.operator)" class="flex gap-2">
              <BaseInput
                :model-value="Array.isArray(condition.value) ? condition.value[0] : ''"
                placeholder="Min"
                size="sm"
                @update:model-value="updateRangeValue(condition.id, 0, $event)"
              />
              <span class="text-sm text-gray-400 self-center">to</span>
              <BaseInput
                :model-value="Array.isArray(condition.value) ? condition.value[1] : ''"
                placeholder="Max"
                size="sm"
                @update:model-value="updateRangeValue(condition.id, 1, $event)"
              />
            </div>
            <!-- Regular input for other operators -->
            <BaseInput
              v-else-if="!['is_empty', 'is_not_empty'].includes(condition.operator)"
              :model-value="condition.value"
              placeholder="Value"
              size="sm"
              @update:model-value="updateCondition(condition.id, 'value', $event)"
            />
            <span v-else class="text-sm text-gray-400 italic">No value needed</span>
          </div>
        </div>
        
        <!-- THEN clause -->
        <div class="grid grid-cols-12 gap-2 items-center mb-3">
          <div class="col-span-1">
            <span class="text-sm font-medium text-green-600">THEN</span>
          </div>
          
          <div class="col-span-4">
            <BaseSelect
              :model-value="condition.thenAction.target || ''"
              :options="userVariableOptions"
              placeholder="Target variable"
              size="sm"
              @update:model-value="updateThenAction(condition.id, 'target', $event)"
            />
          </div>
          
          <div class="col-span-1 text-center">
            <span class="text-sm font-medium">=</span>
          </div>
          
          <div class="col-span-6">
            <div class="flex space-x-1">
              <BaseSelect
                :model-value="getThenSourceVariable(condition)"
                :options="allVariableOptions"
                placeholder="Source"
                size="sm"
                @update:model-value="updateThenExpression(condition.id, 'source', $event)"
              />
              <BaseSelect
                :model-value="getThenOperator(condition)"
                :options="mathOperatorOptions"
                placeholder="Op"
                size="sm"
                @update:model-value="updateThenExpression(condition.id, 'operator', $event)"
              />
              <BaseSelect
                :model-value="getThenTargetVariable(condition)"
                :options="allVariableOptions"
                placeholder="Target"
                size="sm"
                @update:model-value="updateThenExpression(condition.id, 'target', $event)"
              />
            </div>
          </div>
        </div>
        
        <!-- ELSE clause -->
        <div v-if="condition.elseAction" class="grid grid-cols-12 gap-2 items-center">
          <div class="col-span-1">
            <span class="text-sm font-medium text-red-600">ELSE</span>
          </div>
          
          <div class="col-span-4">
            <BaseSelect
              :model-value="condition.elseAction.target || ''"
              :options="userVariableOptions"
              placeholder="Target variable"
              size="sm"
              @update:model-value="updateElseAction(condition.id, 'target', $event)"
            />
          </div>
          
          <div class="col-span-1 text-center">
            <span class="text-sm font-medium">=</span>
          </div>
          
          <div class="col-span-6">
            <BaseInput
              :model-value="condition.elseAction.value || ''"
              placeholder="Value"
              size="sm"
              @update:model-value="updateElseAction(condition.id, 'value', $event)"
            />
          </div>
        </div>
        
        <!-- Add ELSE button -->
        <div v-else class="mt-2">
          <BaseButton
            variant="ghost"
            size="sm"
            @click="addElseAction(condition.id)"
          >
            + Add ELSE clause
          </BaseButton>
        </div>
      </div>
    </div>
    
    <div v-else class="text-center py-12">
      <div class="text-gray-400 text-6xl mb-4">🔀</div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Conditions Defined</h3>
      <p class="text-gray-500 mb-4">
        Conditions define the logic of your expression. Add your first condition to get started.
      </p>
      <BaseButton
        variant="primary"
        @click="$emit('add')"
      >
        Add Your First Condition
      </BaseButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Condition, Variable } from '@/types'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'

export interface ConditionBuilderProps {
  conditions: readonly Condition[]
  variables: readonly Variable[]
}

const props = defineProps<ConditionBuilderProps>()

const emit = defineEmits<{
  update: [conditionId: string, field: keyof Condition, value: any]
  updateThenAction: [conditionId: string, field: string, value: any]
  updateElseAction: [conditionId: string, field: string, value: any]
  updateThenExpression: [conditionId: string, part: 'source' | 'operator' | 'target', value: any]
  addElseAction: [conditionId: string]
  add: []
  remove: [conditionId: string]
}>()

const variableOptions = computed(() =>
  props.variables.map(v => ({
    value: v.id,
    label: v.name
  }))
)

const userVariableOptions = computed(() =>
  props.variables
    .filter(v => !v.id.startsWith('total_sales') && !v.id.startsWith('par')) // Filter out reserved variables
    .map(v => ({
      value: v.id,
      label: v.name
    }))
)

const allVariableOptions = computed(() => variableOptions.value)

const operatorOptions = [
  { value: 'equals', label: '=' },
  { value: 'not_equals', label: '!=' },
  { value: 'greater_than', label: '>' },
  { value: 'less_than', label: '<' },
  { value: 'greater_equal', label: '>=' },
  { value: 'less_equal', label: '<=' },
  { value: 'is_empty', label: 'is Empty' },
  { value: 'is_not_empty', label: 'is Not Empty' },
  { value: 'between', label: 'is between' },
  { value: 'not_between', label: 'is not between' },
  { value: 'in', label: 'is in' },
  { value: 'not_in', label: 'is not in' }
]

const mathOperatorOptions = [
  { value: '*', label: '×' },
  { value: '+', label: '+' },
  { value: '-', label: '-' },
  { value: '/', label: '÷' }
]

function updateCondition(conditionId: string, field: keyof Condition, value: any) {
  // Emit update event with specific data
  emit('update', conditionId, field, value)
}

function updateRangeValue(conditionId: string, index: number, value: any) {
  // Find the condition and update the range value
  const condition = props.conditions.find(c => c.id === conditionId)
  if (!condition) return

  let rangeValue = Array.isArray(condition.value) ? [...condition.value] : ['', '']
  rangeValue[index] = value

  emit('update', conditionId, 'value', rangeValue)
}

function updateThenAction(conditionId: string, field: string, value: any) {
  // Update then action
  emit('updateThenAction', conditionId, field, value)
}

function updateElseAction(conditionId: string, field: string, value: any) {
  // Update else action
  emit('updateElseAction', conditionId, field, value)
}

function updateThenExpression(conditionId: string, part: 'source' | 'operator' | 'target', value: any) {
  // Update then expression parts
  emit('updateThenExpression', conditionId, part, value)
}

function addElseAction(conditionId: string) {
  // Add else action to condition
  emit('addElseAction', conditionId)
}

function removeCondition(conditionId: string) {
  emit('remove', conditionId)
}

// Helper functions to extract expression parts
function getThenSourceVariable(condition: Condition): string {
  // Parse the expression to get source variable
  const expr = condition.thenAction.expression || ''
  const parts = expr.split(' ')
  return parts[0] || ''
}

function getThenOperator(condition: Condition): string {
  // Parse the expression to get operator
  const expr = condition.thenAction.expression || ''
  const parts = expr.split(' ')
  return parts[1] || '*'
}

function getThenTargetVariable(condition: Condition): string {
  // Parse the expression to get target variable
  const expr = condition.thenAction.expression || ''
  const parts = expr.split(' ')
  return parts[2] || ''
}
</script>
