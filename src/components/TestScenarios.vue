<template>
  <div class="test-scenarios">
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-2">Test Scenarios</h2>
      <p class="text-gray-600">
        Select a predefined scenario to automatically configure variables and conditions
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div
        v-for="scenario in scenarios"
        :key="scenario.id"
        class="scenario-card border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-primary-300 hover:shadow-md transition-all"
        :class="{ 'border-primary-500 bg-primary-50': selectedScenario?.id === scenario.id }"
        @click="selectScenario(scenario)"
      >
        <div class="flex items-start justify-between mb-3">
          <h3 class="font-medium text-gray-900">{{ scenario.name }}</h3>
          <div class="text-2xl">{{ scenario.icon }}</div>
        </div>
        <p class="text-sm text-gray-600 mb-3">{{ scenario.description }}</p>
        <div class="text-xs text-gray-500">
          <div>Variables: {{ scenario.variables.length }}</div>
          <div>Conditions: {{ scenario.conditionSets.length }} sets</div>
        </div>
      </div>
    </div>

    <div v-if="selectedScenario" class="bg-gray-50 border border-gray-200 rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">
          {{ selectedScenario.name }} Configuration
        </h3>
        <BaseButton
          variant="primary"
          @click="implementScenario"
          :disabled="isImplementing"
        >
          {{ isImplementing ? 'Implementing...' : 'Implement Scenario' }}
        </BaseButton>
      </div>

      <div class="space-y-4">
        <!-- Variables Preview -->
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Variables to be created:</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
            <div
              v-for="variable in selectedScenario.variables"
              :key="variable.name"
              class="bg-white border border-gray-200 rounded p-3"
            >
              <div class="font-medium text-sm">{{ variable.name }}</div>
              <div class="text-xs text-gray-500">{{ variable.type }} - {{ variable.description }}</div>
            </div>
          </div>
        </div>

        <!-- Condition Sets Preview -->
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Logic to be implemented:</h4>
          <div class="space-y-3">
            <div
              v-for="(conditionSet, index) in selectedScenario.conditionSets"
              :key="index"
              class="bg-white border border-gray-200 rounded p-3"
            >
              <div class="font-medium text-sm mb-2">{{ conditionSet.name }}</div>
              <div class="text-sm text-gray-700 font-mono bg-gray-50 p-2 rounded">
                {{ conditionSet.expression }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Variable, ConditionSet } from '@/types'
import BaseButton from '@/components/ui/BaseButton.vue'

interface TestScenario {
  id: string
  name: string
  description: string
  icon: string
  variables: Array<{
    name: string
    type: string
    description: string
    defaultValue?: any
    options?: Array<{ value: string; label: string }>
  }>
  conditionSets: Array<{
    name: string
    expression: string
    isIndependent: boolean
    priority: number
  }>
}

const emit = defineEmits<{
  implementScenario: [scenario: TestScenario]
}>()

const selectedScenario = ref<TestScenario | null>(null)
const isImplementing = ref(false)

const scenarios: TestScenario[] = [
  {
    id: 'end-month-commission',
    name: 'End Month Commission',
    description: 'Prioritized rate selection with fallback logic for commission calculation',
    icon: '📅',
    variables: [
      {
        name: 'Inclusive Rate',
        type: 'percentage',
        description: 'Rate applied regardless of loan type'
      },
      {
        name: 'New Loan Rate',
        type: 'percentage',
        description: 'Rate for new loans only'
      },
      {
        name: 'Repeat Loan Rate',
        type: 'percentage',
        description: 'Rate for repeat loans only'
      },
      {
        name: 'Final Commission',
        type: 'number',
        description: 'Total commission awarded to the sales associate'
      }
    ],
    conditionSets: [
      {
        name: 'Priority Rate Selection',
        expression: 'IF(Inclusive Rate is not empty) THEN Final Commission = Total Sales × Inclusive Rate ELSE IF(New Loan Rate is not empty) THEN Final Commission = Total Sales × New Loan Rate ELSE IF(Repeat Loan Rate is not empty) THEN Final Commission = Total Sales × Repeat Loan Rate ELSE Final Commission = 0',
        isIndependent: false,
        priority: 1
      }
    ]
  },
  {
    id: 'weekly-loan-incentive',
    name: 'Weekly Loan Type Incentive',
    description: 'Complex multi-condition scenario with three independent condition sets',
    icon: '📊',
    variables: [
      {
        name: 'Inclusive Rate',
        type: 'percentage',
        description: 'Rate used irrespective of loan type'
      },
      {
        name: 'New Loan Rate',
        type: 'percentage',
        description: 'Rate used for new loans'
      },
      {
        name: 'Repeat Loan Rate',
        type: 'percentage',
        description: 'Rate used for repeat loans'
      },
      {
        name: 'Fixed Amount',
        type: 'number',
        description: 'Fixed amount to be awarded to the sales associate'
      },
      {
        name: 'Final Commission',
        type: 'number',
        description: 'Total commission awarded to the sales associate'
      }
    ],
    conditionSets: [
      {
        name: 'Tiered Fixed Amount',
        expression: 'IF(Fixed Amount is not empty) THEN Final Commission = Fixed Amount ELSE IF(Total Sales < 100,000) THEN Final Commission = 0 ELSE IF(Total Sales < 200,000) THEN Final Commission = 2,000 ELSE IF(Total Sales < 300,000) THEN Final Commission = 4,000 ELSE IF(Total Sales < 400,000) THEN Final Commission = 6,000 ELSE Final Commission = 8,000',
        isIndependent: true,
        priority: 1
      },
      {
        name: 'Target-Based Rate',
        expression: 'IF(Total Sales >= Weekly Target) THEN Final Commission = Total Sales × Inclusive Rate ELSE Final Commission = 0',
        isIndependent: true,
        priority: 2
      },
      {
        name: 'Loan Type Rates',
        expression: 'IF(Loan Type = "Buyoff") THEN Final Commission = Total Sales × 1.5% ELSE IF(Loan Type = "Top-up") THEN Final Commission = Total Sales × 1% ELSE IF(Loan Type = "New") THEN Final Commission = Total Sales × 2% ELSE Final Commission = 0',
        isIndependent: true,
        priority: 3
      }
    ]
  },
  {
    id: 'portfolio-commission',
    name: 'Portfolio Commission',
    description: 'Range-based and multi-select condition scenario with PAR requirements',
    icon: '💼',
    variables: [
      {
        name: 'Portfolio Size',
        type: 'number',
        description: 'Size of the portfolio'
      },
      {
        name: 'Portfolio Target',
        type: 'number',
        description: 'Portfolio target for the portfolio'
      },
      {
        name: 'Portfolio Sales',
        type: 'number',
        description: 'Portfolio sales for the portfolio'
      },
      {
        name: 'PAR Days',
        type: 'number',
        description: 'Number of days in PAR'
      },
      {
        name: 'PAR Percentage',
        type: 'percentage',
        description: 'Maximum acceptable PAR threshold'
      },
      {
        name: 'Fixed Amount',
        type: 'number',
        description: 'Fixed amount to be awarded to the sales associate'
      },
      {
        name: 'Commission Rate',
        type: 'percentage',
        description: 'Commission rate to be applied to the portfolio'
      },
      {
        name: 'Final Commission',
        type: 'number',
        description: 'Total commission awarded to the sales associate'
      },
      {
        name: 'Portfolio Category',
        type: 'multi-select',
        description: 'Category of the portfolio',
        options: [
          { value: 'fixed_amount', label: 'Fixed Amount' },
          { value: 'rate_based', label: 'Rate Based' },
          { value: 'target_based', label: 'Target Based' }
        ]
      },
      {
        name: 'Loan Type',
        type: 'multi-select',
        description: 'Type of loan',
        options: [
          { value: 'buyoff', label: 'Buyoff' },
          { value: 'topup', label: 'Top-up' },
          { value: 'new', label: 'New' }
        ]
      }
    ],
    conditionSets: [
      {
        name: 'Category-Based Commission',
        expression: 'IF(Portfolio Category = "Fixed Amount" AND PAR(PAR Days) < PAR Percentage AND Portfolio Size BETWEEN 100,000 AND 200,000) THEN Final Commission = 3,000 ELSE IF(Portfolio Category = "Rate Based" AND PAR(PAR Days) < PAR Percentage) THEN Final Commission = Portfolio Size × Commission Rate ELSE IF(Portfolio Category = "Target Based" AND Net Sales > Portfolio Target AND PAR(PAR Days) < PAR Percentage) THEN Final Commission = Portfolio Size × Commission Rate ELSE Final Commission = 0',
        isIndependent: false,
        priority: 1
      }
    ]
  }
]

function selectScenario(scenario: TestScenario) {
  selectedScenario.value = scenario
}

async function implementScenario() {
  if (!selectedScenario.value) return
  
  isImplementing.value = true
  try {
    emit('implementScenario', selectedScenario.value)
  } finally {
    isImplementing.value = false
  }
}
</script>

<style scoped>
.scenario-card {
  transition: all 0.2s ease;
}

.scenario-card:hover {
  transform: translateY(-1px);
}
</style>
