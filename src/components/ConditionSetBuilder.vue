<template>
  <div class="condition-set-builder">
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Condition Sets</h3>
        <BaseButton
          variant="primary"
          @click="addConditionSet"
        >
          + Add Condition Set
        </BaseButton>
      </div>
      
      <p class="text-sm text-gray-600">
        Create multiple independent condition sets for complex logic scenarios.
        Each set can be evaluated separately or in sequence.
      </p>
    </div>

    <div v-if="conditionSets.length === 0" class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
      <div class="text-gray-400 text-6xl mb-4">🔗</div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Condition Sets</h3>
      <p class="text-gray-500 mb-4">
        Create condition sets to organize complex logic into manageable groups
      </p>
      <BaseButton
        variant="primary"
        @click="addConditionSet"
      >
        Create Your First Condition Set
      </BaseButton>
    </div>

    <div v-else class="space-y-6">
      <div
        v-for="(conditionSet, setIndex) in conditionSets"
        :key="conditionSet.id"
        class="condition-set bg-white border border-gray-200 rounded-lg"
      >
        <!-- Condition Set Header -->
        <div class="p-4 border-b border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-gray-700">Set {{ setIndex + 1 }}:</span>
                <BaseInput
                  :model-value="conditionSet.name"
                  placeholder="Condition set name"
                  size="sm"
                  class="w-48"
                  @update:model-value="updateConditionSet(conditionSet.id, 'name', $event)"
                />
              </div>
              
              <div class="flex items-center space-x-2">
                <label class="text-sm text-gray-600">Priority:</label>
                <BaseInput
                  :model-value="conditionSet.priority"
                  type="number"
                  size="sm"
                  class="w-16"
                  @update:model-value="updateConditionSet(conditionSet.id, 'priority', parseInt($event))"
                />
              </div>
              
              <div class="flex items-center space-x-2">
                <input
                  :id="`independent-${conditionSet.id}`"
                  :checked="conditionSet.isIndependent"
                  type="checkbox"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  @change="updateConditionSet(conditionSet.id, 'isIndependent', $event.target.checked)"
                />
                <label :for="`independent-${conditionSet.id}`" class="text-sm text-gray-600">
                  Independent
                </label>
              </div>
            </div>
            
            <BaseButton
              variant="danger"
              size="sm"
              @click="removeConditionSet(conditionSet.id)"
            >
              ×
            </BaseButton>
          </div>
          
          <div v-if="conditionSet.description" class="mt-2">
            <p class="text-sm text-gray-600">{{ conditionSet.description }}</p>
          </div>
        </div>

        <!-- Conditions in this set -->
        <div class="p-4">
          <div class="flex items-center justify-between mb-4">
            <h4 class="text-sm font-medium text-gray-900">Conditions</h4>
            <BaseButton
              variant="secondary"
              size="sm"
              @click="addConditionToSet(conditionSet.id)"
            >
              + Add Condition
            </BaseButton>
          </div>

          <div v-if="conditionSet.conditions.length === 0" class="text-center py-8 text-gray-500">
            <div class="text-2xl mb-2">📝</div>
            <p class="text-sm">No conditions in this set</p>
            <p class="text-xs">Add conditions to define the logic for this set</p>
          </div>

          <div v-else class="space-y-3">
            <div
              v-for="(condition, condIndex) in conditionSet.conditions"
              :key="condition.id"
              class="condition-item bg-gray-50 border border-gray-200 rounded p-3"
            >
              <div class="flex items-center justify-between mb-2">
                <span class="text-xs font-medium text-gray-600">
                  Condition {{ condIndex + 1 }}
                </span>
                <BaseButton
                  variant="danger"
                  size="sm"
                  @click="removeConditionFromSet(conditionSet.id, condition.id)"
                >
                  ×
                </BaseButton>
              </div>

              <!-- Simplified condition builder for each condition -->
              <div class="grid grid-cols-12 gap-2 items-center">
                <div class="col-span-1">
                  <span class="text-xs font-medium text-primary-600">IF</span>
                </div>
                
                <div class="col-span-3">
                  <BaseSelect
                    :model-value="condition.variable"
                    :options="variableOptions"
                    placeholder="Variable"
                    size="sm"
                    @update:model-value="updateConditionInSet(conditionSet.id, condition.id, 'variable', $event)"
                  />
                </div>
                
                <div class="col-span-2">
                  <BaseSelect
                    :model-value="condition.operator"
                    :options="operatorOptions"
                    placeholder="Op"
                    size="sm"
                    @update:model-value="updateConditionInSet(conditionSet.id, condition.id, 'operator', $event)"
                  />
                </div>
                
                <div class="col-span-2">
                  <BaseInput
                    v-if="!['is_empty', 'is_not_empty'].includes(condition.operator)"
                    :model-value="condition.value"
                    placeholder="Value"
                    size="sm"
                    @update:model-value="updateConditionInSet(conditionSet.id, condition.id, 'value', $event)"
                  />
                </div>
                
                <div class="col-span-1">
                  <span class="text-xs font-medium text-green-600">THEN</span>
                </div>
                
                <div class="col-span-3">
                  <BaseInput
                    :model-value="condition.thenAction.value"
                    placeholder="Result"
                    size="sm"
                    @update:model-value="updateConditionActionInSet(conditionSet.id, condition.id, 'thenAction', 'value', $event)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ConditionSet, Condition, Variable, ComparisonOperator } from '@/types'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'

export interface ConditionSetBuilderProps {
  conditionSets: ConditionSet[]
  variables: Variable[]
}

const props = defineProps<ConditionSetBuilderProps>()

const emit = defineEmits<{
  addConditionSet: []
  removeConditionSet: [setId: string]
  updateConditionSet: [setId: string, field: string, value: any]
  addConditionToSet: [setId: string]
  removeConditionFromSet: [setId: string, conditionId: string]
  updateConditionInSet: [setId: string, conditionId: string, field: string, value: any]
  updateConditionActionInSet: [setId: string, conditionId: string, actionType: string, field: string, value: any]
}>()

const variableOptions = computed(() =>
  props.variables.map(v => ({
    value: v.id,
    label: v.name
  }))
)

const operatorOptions = [
  { value: 'equals', label: '=' },
  { value: 'not_equals', label: '!=' },
  { value: 'greater_than', label: '>' },
  { value: 'less_than', label: '<' },
  { value: 'greater_equal', label: '>=' },
  { value: 'less_equal', label: '<=' },
  { value: 'is_empty', label: 'is Empty' },
  { value: 'is_not_empty', label: 'is Not Empty' },
  { value: 'between', label: 'is between' },
  { value: 'not_between', label: 'is not between' }
]

function addConditionSet() {
  emit('addConditionSet')
}

function removeConditionSet(setId: string) {
  emit('removeConditionSet', setId)
}

function updateConditionSet(setId: string, field: string, value: any) {
  emit('updateConditionSet', setId, field, value)
}

function addConditionToSet(setId: string) {
  emit('addConditionToSet', setId)
}

function removeConditionFromSet(setId: string, conditionId: string) {
  emit('removeConditionFromSet', setId, conditionId)
}

function updateConditionInSet(setId: string, conditionId: string, field: string, value: any) {
  emit('updateConditionInSet', setId, conditionId, field, value)
}

function updateConditionActionInSet(setId: string, conditionId: string, actionType: string, field: string, value: any) {
  emit('updateConditionActionInSet', setId, conditionId, actionType, field, value)
}
</script>

<style scoped>
.condition-set {
  transition: all 0.2s ease;
}

.condition-set:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
</style>
