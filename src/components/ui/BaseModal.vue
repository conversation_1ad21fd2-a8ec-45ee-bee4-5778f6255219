<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="modelValue"
        class="fixed inset-0 z-50 overflow-y-auto"
        @click="handleBackdropClick"
      >
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" />
        
        <!-- Modal container -->
        <div class="flex min-h-full items-center justify-center p-4">
          <Transition
            enter-active-class="transition-all duration-300"
            enter-from-class="opacity-0 scale-95"
            enter-to-class="opacity-100 scale-100"
            leave-active-class="transition-all duration-300"
            leave-from-class="opacity-100 scale-100"
            leave-to-class="opacity-0 scale-95"
          >
            <div
              v-if="modelValue"
              :class="modalClasses"
              @click.stop
            >
              <!-- Header -->
              <div v-if="$slots.header || title" class="modal-header">
                <slot name="header">
                  <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">{{ title }}</h2>
                    <BaseButton
                      v-if="showCloseButton"
                      variant="ghost"
                      size="sm"
                      @click="handleClose"
                    >
                      ×
                    </BaseButton>
                  </div>
                </slot>
              </div>
              
              <!-- Body -->
              <div class="modal-body">
                <slot />
              </div>
              
              <!-- Footer -->
              <div v-if="$slots.footer" class="modal-footer">
                <slot name="footer" />
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, watch, nextTick } from 'vue'
import BaseButton from './BaseButton.vue'

export interface BaseModalProps {
  modelValue: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  showCloseButton?: boolean
  closeOnBackdrop?: boolean
  closeOnEscape?: boolean
}

const props = withDefaults(defineProps<BaseModalProps>(), {
  size: 'md',
  showCloseButton: true,
  closeOnBackdrop: true,
  closeOnEscape: true
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
  open: []
}>()

const modalClasses = computed(() => {
  const baseClasses = [
    'relative',
    'bg-white',
    'rounded-lg',
    'shadow-xl',
    'transform',
    'transition-all',
    'max-h-[90vh]',
    'overflow-hidden',
    'flex',
    'flex-col'
  ]

  const sizeClasses = {
    sm: ['w-full', 'max-w-sm'],
    md: ['w-full', 'max-w-md'],
    lg: ['w-full', 'max-w-lg'],
    xl: ['w-full', 'max-w-xl'],
    full: ['w-full', 'max-w-4xl']
  }

  return [
    ...baseClasses,
    ...sizeClasses[props.size]
  ].join(' ')
})

function handleClose() {
  emit('update:modelValue', false)
  emit('close')
}

function handleBackdropClick() {
  if (props.closeOnBackdrop) {
    handleClose()
  }
}

function handleEscapeKey(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.closeOnEscape && props.modelValue) {
    handleClose()
  }
}

// Watch for modal open/close to manage body scroll and keyboard events
watch(() => props.modelValue, (isOpen) => {
  if (isOpen) {
    document.body.style.overflow = 'hidden'
    document.addEventListener('keydown', handleEscapeKey)
    emit('open')
  } else {
    document.body.style.overflow = ''
    document.removeEventListener('keydown', handleEscapeKey)
  }
}, { immediate: true })

// Cleanup on unmount
import { onUnmounted } from 'vue'
onUnmounted(() => {
  document.body.style.overflow = ''
  document.removeEventListener('keydown', handleEscapeKey)
})
</script>

<style scoped>
@reference "tailwindcss";

.modal-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.modal-body {
  @apply px-6 py-4 flex-1 overflow-y-auto;
}

.modal-footer {
  @apply px-6 py-4 border-t border-gray-200 flex justify-end space-x-2;
}
</style>
