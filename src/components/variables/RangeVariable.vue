<template>
  <div class="range-variable space-y-4">
    <!-- Range Configuration -->
    <div class="grid grid-cols-3 gap-4">
      <div class="form-group">
        <label class="form-label">Minimum Value</label>
        <BaseInput
          v-model="min"
          type="number"
          :step="step"
          placeholder="0"
          @update:model-value="updateMin"
        />
      </div>
      
      <div class="form-group">
        <label class="form-label">Maximum Value</label>
        <BaseInput
          v-model="max"
          type="number"
          :step="step"
          placeholder="100"
          @update:model-value="updateMax"
        />
      </div>
      
      <div class="form-group">
        <label class="form-label">Step</label>
        <BaseInput
          v-model="step"
          type="number"
          min="0.01"
          placeholder="1"
          @update:model-value="updateStep"
        />
      </div>
    </div>

    <!-- Operator Selection -->
    <div class="form-group">
      <label class="form-label">Range Operator</label>
      <BaseSelect
        v-model="operator"
        :options="operatorOptions"
        placeholder="Select operator"
        @update:model-value="updateOperator"
      />
      <p class="text-xs text-gray-500 mt-1">
        {{ getOperatorDescription(operator) }}
      </p>
    </div>

    <!-- Value Input/Display -->
    <div class="form-group">
      <label class="form-label">Current Value</label>
      
      <!-- Single value input for most operators -->
      <div v-if="operator !== 'between'" class="space-y-2">
        <BaseInput
          v-model="singleValue"
          type="number"
          :min="min"
          :max="max"
          :step="step"
          @update:model-value="updateSingleValue"
        />
        
        <!-- Range slider -->
        <div class="px-2">
          <input
            v-model="singleValue"
            type="range"
            :min="min"
            :max="max"
            :step="step"
            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            @input="updateSingleValue(Number($event.target.value))"
          />
          <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>{{ min }}</span>
            <span>{{ max }}</span>
          </div>
        </div>
      </div>
      
      <!-- Range input for 'between' operator -->
      <div v-else class="space-y-2">
        <div class="grid grid-cols-2 gap-2">
          <BaseInput
            v-model="rangeStart"
            type="number"
            :min="min"
            :max="rangeEnd"
            :step="step"
            placeholder="From"
            @update:model-value="updateRangeStart"
          />
          <BaseInput
            v-model="rangeEnd"
            type="number"
            :min="rangeStart"
            :max="max"
            :step="step"
            placeholder="To"
            @update:model-value="updateRangeEnd"
          />
        </div>
        
        <!-- Dual range slider -->
        <div class="px-2 relative">
          <div class="relative h-2 bg-gray-200 rounded-lg">
            <div
              class="absolute h-2 bg-primary-500 rounded-lg"
              :style="{
                left: `${((rangeStart - min) / (max - min)) * 100}%`,
                width: `${((rangeEnd - rangeStart) / (max - min)) * 100}%`
              }"
            />
          </div>
          <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>{{ min }}</span>
            <span>{{ max }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Tiers Configuration -->
    <div class="form-group">
      <div class="flex items-center justify-between mb-2">
        <label class="form-label">Tiers (Optional)</label>
        <BaseButton
          variant="secondary"
          size="sm"
          @click="addTier"
        >
          + Add Tier
        </BaseButton>
      </div>
      
      <div v-if="tiers.length > 0" class="space-y-2">
        <div
          v-for="(tier, index) in tiers"
          :key="tier.id"
          class="flex items-center space-x-2 p-3 border border-gray-200 rounded-md"
          :class="{ 'bg-primary-50 border-primary-200': isCurrentTier(tier) }"
        >
          <div class="flex-1 grid grid-cols-4 gap-2">
            <BaseInput
              v-model="tier.name"
              placeholder="Tier name"
              size="sm"
              @update:model-value="updateTier(index, 'name', $event)"
            />
            <BaseInput
              v-model="tier.min"
              type="number"
              :step="step"
              placeholder="Min"
              size="sm"
              @update:model-value="updateTier(index, 'min', Number($event))"
            />
            <BaseInput
              v-model="tier.max"
              type="number"
              :step="step"
              placeholder="Max"
              size="sm"
              @update:model-value="updateTier(index, 'max', Number($event))"
            />
            <BaseInput
              v-model="tier.value"
              placeholder="Tier value"
              size="sm"
              @update:model-value="updateTier(index, 'value', $event)"
            />
          </div>
          
          <BaseButton
            variant="danger"
            size="sm"
            @click="removeTier(index)"
            title="Remove tier"
          >
            ×
          </BaseButton>
        </div>
      </div>
      
      <div v-else class="text-center py-4 text-gray-500 text-sm">
        No tiers defined. Tiers allow you to categorize ranges into named groups.
      </div>
    </div>

    <!-- Current Status Display -->
    <div class="bg-gray-50 p-3 rounded-md">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Current Status:</h4>
      <div class="space-y-1 text-sm">
        <div>
          <span class="text-gray-600">Value:</span>
          <span class="ml-2 font-medium">{{ formatCurrentValue() }}</span>
        </div>
        <div>
          <span class="text-gray-600">Condition:</span>
          <span class="ml-2 font-medium">{{ formatCondition() }}</span>
        </div>
        <div v-if="currentTier">
          <span class="text-gray-600">Tier:</span>
          <span class="ml-2 font-medium text-primary-600">{{ currentTier.name }}</span>
        </div>
      </div>
    </div>

    <!-- Validation Messages -->
    <div v-if="validationErrors.length > 0" class="space-y-1">
      <p
        v-for="error in validationErrors"
        :key="error"
        class="text-sm text-red-600"
      >
        {{ error }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { RangeOperator, RangeTier } from '@/types'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'
import BaseButton from '@/components/ui/BaseButton.vue'

export interface RangeVariableProps {
  min: number
  max: number
  step?: number
  operator: RangeOperator
  value: number | [number, number]
  tiers?: RangeTier[]
}

const props = withDefaults(defineProps<RangeVariableProps>(), {
  step: 1,
  tiers: () => []
})

const emit = defineEmits<{
  'update:min': [min: number]
  'update:max': [max: number]
  'update:step': [step: number]
  'update:operator': [operator: RangeOperator]
  'update:value': [value: number | [number, number]]
  'update:tiers': [tiers: RangeTier[]]
}>()

// Local reactive state
const min = ref(props.min)
const max = ref(props.max)
const step = ref(props.step)
const operator = ref(props.operator)
const tiers = ref<RangeTier[]>([...props.tiers])

// Value handling for different operators
const singleValue = ref(Array.isArray(props.value) ? props.value[0] : props.value)
const rangeStart = ref(Array.isArray(props.value) ? props.value[0] : props.min)
const rangeEnd = ref(Array.isArray(props.value) ? props.value[1] : props.max)

const operatorOptions = [
  { value: 'equals', label: 'Equals (=)' },
  { value: 'greater_than', label: 'Greater than (>)' },
  { value: 'less_than', label: 'Less than (<)' },
  { value: 'greater_equal', label: 'Greater than or equal (>=)' },
  { value: 'less_equal', label: 'Less than or equal (<=)' },
  { value: 'between', label: 'Between (range)' }
]

const currentTier = computed(() => {
  const currentValue = operator.value === 'between' ? rangeStart.value : singleValue.value
  return tiers.value.find(tier => currentValue >= tier.min && currentValue <= tier.max)
})

const validationErrors = computed(() => {
  const errors: string[] = []
  
  if (min.value >= max.value) {
    errors.push('Minimum value must be less than maximum value')
  }
  
  if (step.value <= 0) {
    errors.push('Step must be greater than 0')
  }
  
  if (operator.value === 'between') {
    if (rangeStart.value >= rangeEnd.value) {
      errors.push('Range start must be less than range end')
    }
  }
  
  // Validate tiers
  tiers.value.forEach((tier, index) => {
    if (!tier.name.trim()) {
      errors.push(`Tier ${index + 1} must have a name`)
    }
    if (tier.min >= tier.max) {
      errors.push(`Tier ${index + 1}: minimum must be less than maximum`)
    }
  })
  
  return errors
})

// Watch for prop changes
watch(() => props.value, (newValue) => {
  if (Array.isArray(newValue)) {
    rangeStart.value = newValue[0]
    rangeEnd.value = newValue[1]
  } else {
    singleValue.value = newValue
  }
})

function getOperatorDescription(op: RangeOperator): string {
  const descriptions: Record<RangeOperator, string> = {
    'equals': 'Value must exactly equal the specified number',
    'greater_than': 'Value must be greater than the specified number',
    'less_than': 'Value must be less than the specified number',
    'greater_equal': 'Value must be greater than or equal to the specified number',
    'less_equal': 'Value must be less than or equal to the specified number',
    'between': 'Value must be within the specified range (inclusive)'
  }
  return descriptions[op] || ''
}

function formatCurrentValue(): string {
  if (operator.value === 'between') {
    return `${rangeStart.value} - ${rangeEnd.value}`
  }
  return String(singleValue.value)
}

function formatCondition(): string {
  const value = formatCurrentValue()
  const symbols: Record<RangeOperator, string> = {
    'equals': '=',
    'greater_than': '>',
    'less_than': '<',
    'greater_equal': '>=',
    'less_equal': '<=',
    'between': 'between'
  }
  
  if (operator.value === 'between') {
    return `between ${rangeStart.value} and ${rangeEnd.value}`
  }
  
  return `${symbols[operator.value]} ${value}`
}

function isCurrentTier(tier: RangeTier): boolean {
  return currentTier.value?.id === tier.id
}

// Update handlers
function updateMin(newMin: number) {
  min.value = newMin
  emit('update:min', newMin)
}

function updateMax(newMax: number) {
  max.value = newMax
  emit('update:max', newMax)
}

function updateStep(newStep: number) {
  step.value = newStep
  emit('update:step', newStep)
}

function updateOperator(newOperator: RangeOperator) {
  operator.value = newOperator
  emit('update:operator', newOperator)
  
  // Update value format based on operator
  if (newOperator === 'between') {
    emit('update:value', [rangeStart.value, rangeEnd.value])
  } else {
    emit('update:value', singleValue.value)
  }
}

function updateSingleValue(newValue: number) {
  singleValue.value = newValue
  emit('update:value', newValue)
}

function updateRangeStart(newStart: number) {
  rangeStart.value = newStart
  emit('update:value', [newStart, rangeEnd.value])
}

function updateRangeEnd(newEnd: number) {
  rangeEnd.value = newEnd
  emit('update:value', [rangeStart.value, newEnd])
}

// Tier management
function addTier() {
  const newTier: RangeTier = {
    id: `tier_${Date.now()}`,
    name: `Tier ${tiers.value.length + 1}`,
    min: min.value,
    max: max.value,
    value: ''
  }
  tiers.value.push(newTier)
  emit('update:tiers', tiers.value)
}

function removeTier(index: number) {
  tiers.value.splice(index, 1)
  emit('update:tiers', tiers.value)
}

function updateTier(index: number, field: keyof RangeTier, value: any) {
  tiers.value[index][field] = value
  emit('update:tiers', tiers.value)
}
</script>

<style scoped>
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #1a73e8;
  cursor: pointer;
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #1a73e8;
  cursor: pointer;
  border: none;
}
</style>
