<template>
  <div class="multi-select-variable space-y-4">
    <!-- Logic Type Selection -->
    <div class="form-group">
      <label class="form-label">Logic Type</label>
      <BaseSelect
        v-model="logicType"
        :options="logicTypeOptions"
        placeholder="Select logic type"
        @update:model-value="updateLogicType"
      />
      <p class="text-xs text-gray-500 mt-1">
        {{ logicType === 'AND' ? 'All selected options must be true' : 'Any selected option can be true' }}
      </p>
    </div>

    <!-- Options Management -->
    <div class="form-group">
      <div class="flex items-center justify-between mb-2">
        <label class="form-label">Options</label>
        <BaseButton
          variant="secondary"
          size="sm"
          @click="addOption"
        >
          + Add Option
        </BaseButton>
      </div>

      <div v-if="options.length > 0" class="space-y-2">
        <div
          v-for="(option, index) in options"
          :key="option.value"
          class="flex items-center space-x-2 p-3 border border-gray-200 rounded-md"
        >
          <input
            type="checkbox"
            :id="`option-${index}`"
            :checked="selectedValues.includes(option.value)"
            @change="toggleOption(option.value)"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          
          <div class="flex-1 grid grid-cols-2 gap-2">
            <BaseInput
              v-model="option.label"
              placeholder="Option label"
              size="sm"
              @update:model-value="updateOption(index, 'label', $event)"
            />
            <BaseInput
              v-model="option.value"
              placeholder="Option value"
              size="sm"
              @update:model-value="updateOption(index, 'value', $event)"
            />
          </div>
          
          <BaseButton
            variant="danger"
            size="sm"
            @click="removeOption(index)"
            title="Remove option"
          >
            ×
          </BaseButton>
        </div>
      </div>
      
      <div v-else class="text-center py-8 text-gray-500">
        <p>No options defined</p>
        <p class="text-sm">Click "Add Option" to create your first option</p>
      </div>
    </div>

    <!-- Selection Constraints -->
    <div class="grid grid-cols-2 gap-4">
      <div class="form-group">
        <label class="form-label">Minimum Selections</label>
        <BaseInput
          v-model="minSelections"
          type="number"
          :min="0"
          :max="options.length"
          placeholder="0"
          @update:model-value="updateMinSelections"
        />
      </div>
      
      <div class="form-group">
        <label class="form-label">Maximum Selections</label>
        <BaseInput
          v-model="maxSelections"
          type="number"
          :min="minSelections || 1"
          :max="options.length"
          placeholder="No limit"
          @update:model-value="updateMaxSelections"
        />
      </div>
    </div>

    <!-- Current Selection Summary -->
    <div v-if="selectedValues.length > 0" class="bg-gray-50 p-3 rounded-md">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Current Selection:</h4>
      <div class="flex flex-wrap gap-1">
        <span
          v-for="value in selectedValues"
          :key="value"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary-100 text-primary-800"
        >
          {{ getOptionLabel(value) }}
          <button
            @click="toggleOption(value)"
            class="ml-1 text-primary-600 hover:text-primary-800"
          >
            ×
          </button>
        </span>
      </div>
      
      <div class="mt-2 text-xs text-gray-600">
        Logic: <span class="font-medium">{{ logicType }}</span> |
        Selected: <span class="font-medium">{{ selectedValues.length }}</span> of {{ options.length }}
      </div>
    </div>

    <!-- Validation Messages -->
    <div v-if="validationErrors.length > 0" class="space-y-1">
      <p
        v-for="error in validationErrors"
        :key="error"
        class="text-sm text-red-600"
      >
        {{ error }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { SelectOption, LogicType } from '@/types'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'
import BaseButton from '@/components/ui/BaseButton.vue'

export interface MultiSelectVariableProps {
  options: SelectOption[]
  selectedValues: string[]
  logicType: LogicType
  minSelections?: number
  maxSelections?: number
}

const props = withDefaults(defineProps<MultiSelectVariableProps>(), {
  minSelections: 0
})

const emit = defineEmits<{
  'update:options': [options: SelectOption[]]
  'update:selectedValues': [values: string[]]
  'update:logicType': [type: LogicType]
  'update:minSelections': [min: number]
  'update:maxSelections': [max: number | undefined]
}>()

// Local reactive state
const options = ref<SelectOption[]>([...props.options])
const selectedValues = ref<string[]>([...props.selectedValues])
const logicType = ref<LogicType>(props.logicType)
const minSelections = ref<number>(props.minSelections || 0)
const maxSelections = ref<number | undefined>(props.maxSelections)

const logicTypeOptions = [
  { value: 'AND', label: 'AND (All selected must be true)' },
  { value: 'OR', label: 'OR (Any selected can be true)' }
]

const validationErrors = computed(() => {
  const errors: string[] = []
  
  if (options.value.length === 0) {
    errors.push('At least one option is required')
  }
  
  // Check for duplicate values
  const values = options.value.map(opt => opt.value)
  const uniqueValues = new Set(values)
  if (values.length !== uniqueValues.size) {
    errors.push('Option values must be unique')
  }
  
  // Check for empty labels or values
  if (options.value.some(opt => !opt.label.trim() || !opt.value.trim())) {
    errors.push('All options must have both label and value')
  }
  
  // Check selection constraints
  if (minSelections.value > 0 && selectedValues.value.length < minSelections.value) {
    errors.push(`At least ${minSelections.value} option(s) must be selected`)
  }
  
  if (maxSelections.value && selectedValues.value.length > maxSelections.value) {
    errors.push(`At most ${maxSelections.value} option(s) can be selected`)
  }
  
  return errors
})

// Watch for prop changes
watch(() => props.options, (newOptions) => {
  options.value = [...newOptions]
}, { deep: true })

watch(() => props.selectedValues, (newValues) => {
  selectedValues.value = [...newValues]
})

watch(() => props.logicType, (newType) => {
  logicType.value = newType
})

// Option management
function addOption() {
  const newOption: SelectOption = {
    value: `option_${Date.now()}`,
    label: `Option ${options.value.length + 1}`
  }
  options.value.push(newOption)
  emit('update:options', options.value)
}

function removeOption(index: number) {
  const removedOption = options.value[index]
  options.value.splice(index, 1)
  
  // Remove from selected values if it was selected
  const selectedIndex = selectedValues.value.indexOf(removedOption.value)
  if (selectedIndex > -1) {
    selectedValues.value.splice(selectedIndex, 1)
    emit('update:selectedValues', selectedValues.value)
  }
  
  emit('update:options', options.value)
}

function updateOption(index: number, field: 'label' | 'value', value: string) {
  const oldValue = options.value[index].value
  options.value[index][field] = value
  
  // If value changed, update selected values
  if (field === 'value' && oldValue !== value) {
    const selectedIndex = selectedValues.value.indexOf(oldValue)
    if (selectedIndex > -1) {
      selectedValues.value[selectedIndex] = value
      emit('update:selectedValues', selectedValues.value)
    }
  }
  
  emit('update:options', options.value)
}

// Selection management
function toggleOption(value: string) {
  const index = selectedValues.value.indexOf(value)
  
  if (index > -1) {
    // Remove selection
    selectedValues.value.splice(index, 1)
  } else {
    // Add selection (check max constraint)
    if (!maxSelections.value || selectedValues.value.length < maxSelections.value) {
      selectedValues.value.push(value)
    }
  }
  
  emit('update:selectedValues', selectedValues.value)
}

function getOptionLabel(value: string): string {
  const option = options.value.find(opt => opt.value === value)
  return option?.label || value
}

// Update handlers
function updateLogicType(type: LogicType) {
  logicType.value = type
  emit('update:logicType', type)
}

function updateMinSelections(min: number) {
  minSelections.value = min
  emit('update:minSelections', min)
}

function updateMaxSelections(max: number) {
  maxSelections.value = max || undefined
  emit('update:maxSelections', max || undefined)
}
</script>
