<template>
  <BaseModal
    :model-value="modelValue"
    :title="variable ? 'Edit Variable' : 'Add New Variable'"
    size="lg"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <form @submit.prevent="handleSubmit">
      <div class="space-y-4">
        <!-- Variable Name -->
        <div class="form-group">
          <label class="form-label">Variable Name *</label>
          <BaseInput
            v-model="formData.name"
            placeholder="Enter variable name"
            required
            :error="errors.name"
          />
        </div>

        <!-- Variable Type -->
        <div class="form-group">
          <label class="form-label">Variable Type *</label>
          <BaseSelect
            v-model="formData.type"
            :options="typeOptions"
            placeholder="Select variable type"
            required
            :error="errors.type"
          />
        </div>

        <!-- Description -->
        <div class="form-group">
          <label class="form-label">Description</label>
          <BaseInput
            v-model="formData.description"
            placeholder="Enter variable description"
          />
        </div>

        <!-- Type-specific configuration -->
        <div v-if="formData.type === 'multi-select'" class="form-group">
          <MultiSelectVariable
            :options="formData.options || []"
            :selected-values="formData.selectedValues || []"
            :logic-type="formData.logicType || 'OR'"
            :min-selections="formData.minSelections"
            :max-selections="formData.maxSelections"
            @update:options="formData.options = $event"
            @update:selected-values="formData.selectedValues = $event"
            @update:logic-type="formData.logicType = $event"
            @update:min-selections="formData.minSelections = $event"
            @update:max-selections="formData.maxSelections = $event"
          />
        </div>

        <div v-else-if="formData.type === 'range'" class="form-group">
          <RangeVariable
            :min="formData.min || 0"
            :max="formData.max || 100"
            :step="formData.step || 1"
            :operator="formData.operator || 'between'"
            :value="formData.value || 0"
            :tiers="formData.tiers || []"
            @update:min="formData.min = $event"
            @update:max="formData.max = $event"
            @update:step="formData.step = $event"
            @update:operator="formData.operator = $event"
            @update:value="formData.value = $event"
            @update:tiers="formData.tiers = $event"
          />
        </div>

        <div v-else-if="formData.type === 'predicate'" class="form-group">
          <PredicateVariable
            :conditions="formData.conditions || []"
            :default-value="formData.defaultValue"
            :dependencies="formData.dependencies || []"
            :available-variables="availableVariableOptions"
            @update:conditions="formData.conditions = $event"
            @update:default-value="formData.defaultValue = $event"
            @update:dependencies="formData.dependencies = $event"
          />
        </div>

        <!-- Default Value for simple types -->
        <div v-else-if="isSimpleType" class="form-group">
          <label class="form-label">Default Value</label>
          <BaseInput
            v-model="formData.defaultValue"
            :type="getInputType(formData.type)"
            placeholder="Enter default value"
          />
        </div>
      </div>
    </form>

    <template #footer>
      <BaseButton
        variant="secondary"
        @click="$emit('cancel')"
      >
        Cancel
      </BaseButton>
      <BaseButton
        variant="primary"
        @click="handleSubmit"
        :disabled="!isValid"
      >
        {{ variable ? 'Update' : 'Add' }} Variable
      </BaseButton>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { Variable, VariableType } from '@/types'
import BaseModal from '@/components/ui/BaseModal.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'
import MultiSelectVariable from './MultiSelectVariable.vue'
import RangeVariable from './RangeVariable.vue'
import PredicateVariable from './PredicateVariable.vue'

export interface VariableModalProps {
  modelValue: boolean
  variable?: Variable | null
}

const props = defineProps<VariableModalProps>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [variable: Variable]
  cancel: []
}>()

const formData = ref<any>({
  name: '',
  type: '',
  description: '',
  defaultValue: ''
})

const errors = ref<Record<string, string>>({})

const typeOptions = [
  { value: 'number', label: 'Number' },
  { value: 'text', label: 'Text' },
  { value: 'percentage', label: 'Percentage' },
  { value: 'currency', label: 'Currency' },
  { value: 'boolean', label: 'Boolean' },
  { value: 'multi-select', label: 'Multi-Select' },
  { value: 'range', label: 'Range' },
  { value: 'predicate', label: 'Calculated/Conditional' }
]

const isSimpleType = computed(() => 
  ['number', 'text', 'percentage', 'currency', 'boolean'].includes(formData.value.type)
)

const isValid = computed(() => {
  return formData.value.name.trim() && formData.value.type && Object.keys(errors.value).length === 0
})

// Watch for variable prop changes
watch(() => props.variable, (variable) => {
  if (variable) {
    formData.value = { ...variable }
  } else {
    resetForm()
  }
}, { immediate: true })

// Watch for modal open/close
watch(() => props.modelValue, (isOpen) => {
  if (isOpen && !props.variable) {
    resetForm()
  }
})

function resetForm() {
  formData.value = {
    name: '',
    type: '',
    description: '',
    defaultValue: ''
  }
  errors.value = {}
}

function getInputType(type: VariableType): string {
  switch (type) {
    case 'number':
    case 'percentage':
    case 'currency':
      return 'number'
    default:
      return 'text'
  }
}

function validateForm(): boolean {
  errors.value = {}
  
  if (!formData.value.name.trim()) {
    errors.value.name = 'Variable name is required'
  }
  
  if (!formData.value.type) {
    errors.value.type = 'Variable type is required'
  }
  
  return Object.keys(errors.value).length === 0
}

function handleSubmit() {
  if (!validateForm()) return
  
  const variable: Variable = {
    id: props.variable?.id || `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: formData.value.name,
    type: formData.value.type,
    description: formData.value.description,
    defaultValue: formData.value.defaultValue,
    createdAt: props.variable?.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...formData.value // Include type-specific properties
  }
  
  emit('save', variable)
}
</script>
