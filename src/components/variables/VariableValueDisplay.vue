<template>
  <div class="variable-value-display">
    <!-- Simple Variable Types -->
    <div v-if="isSimpleType" class="simple-value">
      <span class="text-sm text-gray-600">Value:</span>
      <span class="ml-2 font-medium">{{ formatSimpleValue() }}</span>
    </div>
    
    <!-- Multi-Select Variable -->
    <div v-else-if="variable.type === 'multi-select'" class="multi-select-value">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm text-gray-600">Selected Options:</span>
        <span class="text-xs bg-gray-100 px-2 py-1 rounded">
          {{ multiSelectVar.logicType }}
        </span>
      </div>
      
      <div v-if="multiSelectVar.selectedValues.length > 0" class="flex flex-wrap gap-1">
        <span
          v-for="value in multiSelectVar.selectedValues"
          :key="value"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary-100 text-primary-800"
        >
          {{ getOptionLabel(value) }}
        </span>
      </div>
      <div v-else class="text-sm text-gray-400 italic">
        No options selected
      </div>
    </div>
    
    <!-- Range Variable -->
    <div v-else-if="variable.type === 'range'" class="range-value">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm text-gray-600">Range:</span>
        <span class="text-xs bg-gray-100 px-2 py-1 rounded">
          {{ rangeVar.operator }}
        </span>
      </div>
      
      <div class="space-y-1">
        <div class="flex items-center justify-between text-sm">
          <span>Min: {{ rangeVar.min }}</span>
          <span>Max: {{ rangeVar.max }}</span>
        </div>
        
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div
            class="bg-primary-500 h-2 rounded-full"
            :style="{ width: `${getRangePercentage()}%` }"
          />
        </div>
        
        <div class="text-center text-sm font-medium">
          Current: {{ formatRangeValue() }}
        </div>
        
        <div v-if="rangeVar.tiers && rangeVar.tiers.length > 0" class="mt-2">
          <div class="text-xs text-gray-600 mb-1">Tiers:</div>
          <div class="flex flex-wrap gap-1">
            <span
              v-for="tier in rangeVar.tiers"
              :key="tier.id"
              :class="[
                'inline-flex items-center px-2 py-1 rounded text-xs',
                isInTier(tier) ? 'bg-primary-100 text-primary-800' : 'bg-gray-100 text-gray-600'
              ]"
            >
              {{ tier.name }}
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Predicate Variable -->
    <div v-else-if="variable.type === 'predicate'" class="predicate-value">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm text-gray-600">Conditions:</span>
        <span class="text-xs bg-gray-100 px-2 py-1 rounded">
          {{ predicateVar.conditions.length }} rule{{ predicateVar.conditions.length !== 1 ? 's' : '' }}
        </span>
      </div>
      
      <div v-if="predicateVar.conditions.length > 0" class="space-y-1">
        <div
          v-for="(condition, index) in predicateVar.conditions.slice(0, 2)"
          :key="condition.id"
          class="text-xs bg-gray-50 p-2 rounded border-l-2 border-primary-300"
        >
          <span class="font-medium">Rule {{ index + 1 }}:</span>
          <span class="ml-1">{{ formatConditionSummary(condition) }}</span>
        </div>
        
        <div v-if="predicateVar.conditions.length > 2" class="text-xs text-gray-500 text-center">
          ... and {{ predicateVar.conditions.length - 2 }} more
        </div>
      </div>
      <div v-else class="text-sm text-gray-400 italic">
        No conditions defined
      </div>
      
      <div v-if="predicateVar.dependencies.length > 0" class="mt-2">
        <div class="text-xs text-gray-600 mb-1">Depends on:</div>
        <div class="flex flex-wrap gap-1">
          <span
            v-for="depId in predicateVar.dependencies"
            :key="depId"
            class="inline-flex items-center px-2 py-1 rounded text-xs bg-orange-100 text-orange-800"
          >
            {{ getDependencyName(depId) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { 
  Variable, 
  MultiSelectVariable, 
  RangeVariable, 
  PredicateVariable,
  ConditionalExpression,
  RangeTier
} from '@/types'

export interface VariableValueDisplayProps {
  variable: Variable
}

const props = defineProps<VariableValueDisplayProps>()

const isSimpleType = computed(() => 
  ['number', 'text', 'percentage', 'currency', 'boolean'].includes(props.variable.type)
)

const multiSelectVar = computed(() => props.variable as MultiSelectVariable)
const rangeVar = computed(() => props.variable as RangeVariable)
const predicateVar = computed(() => props.variable as PredicateVariable)

function formatSimpleValue(): string {
  const value = (props.variable as any).value || props.variable.defaultValue
  
  if (value === null || value === undefined) {
    return 'Not set'
  }
  
  if (typeof value === 'boolean') {
    return value ? 'True' : 'False'
  }
  
  if (props.variable.type === 'currency') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(value))
  }
  
  if (props.variable.type === 'percentage') {
    return `${Number(value)}%`
  }
  
  return String(value)
}

function getOptionLabel(value: string): string {
  const option = multiSelectVar.value.options.find(opt => opt.value === value)
  return option?.label || value
}

function getRangePercentage(): number {
  const { min, max, value } = rangeVar.value
  const currentValue = Array.isArray(value) ? value[0] : value
  return ((currentValue - min) / (max - min)) * 100
}

function formatRangeValue(): string {
  const { value } = rangeVar.value
  
  if (Array.isArray(value)) {
    return `${value[0]} - ${value[1]}`
  }
  
  return String(value)
}

function isInTier(tier: RangeTier): boolean {
  const { value } = rangeVar.value
  const currentValue = Array.isArray(value) ? value[0] : value
  return currentValue >= tier.min && currentValue <= tier.max
}

function formatConditionSummary(condition: ConditionalExpression): string {
  // This is a simplified summary - in a real app you'd want more sophisticated formatting
  const { if: ifCondition, then: thenAction } = condition
  
  let summary = `IF ${ifCondition.variable} ${ifCondition.operator}`
  if (ifCondition.value !== null && ifCondition.value !== undefined) {
    summary += ` ${ifCondition.value}`
  }
  
  summary += ` THEN ${thenAction.type}`
  if (thenAction.target) {
    summary += ` ${thenAction.target}`
  }
  
  return summary
}

function getDependencyName(depId: string): string {
  // In a real app, you'd look up the variable name from the dependency ID
  // For now, just return the ID
  return depId.replace(/^var_/, '').substring(0, 8) + '...'
}
</script>
