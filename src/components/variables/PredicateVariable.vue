<template>
  <div class="predicate-variable">
    <div class="space-y-4">
      <!-- Default Value -->
      <div class="form-group">
        <label class="form-label">Default Value</label>
        <BaseInput
          :model-value="defaultValue"
          placeholder="Enter default value"
          @update:model-value="$emit('update:defaultValue', $event)"
        />
        <p class="text-sm text-gray-500 mt-1">
          Value to use when no conditions are met
        </p>
      </div>

      <!-- Dependencies -->
      <div class="form-group">
        <label class="form-label">Dependencies</label>
        <BaseSelect
          :model-value="dependencies"
          :options="availableVariables"
          placeholder="Select variables this depends on"
          multiple
          @update:model-value="$emit('update:dependencies', $event)"
        />
        <p class="text-sm text-gray-500 mt-1">
          Variables that this calculated variable depends on
        </p>
      </div>

      <!-- Conditions -->
      <div class="form-group">
        <div class="flex items-center justify-between mb-3">
          <label class="form-label">Calculation Rules</label>
          <BaseButton
            variant="secondary"
            size="sm"
            @click="addCondition"
          >
            + Add Rule
          </BaseButton>
        </div>

        <div v-if="conditions.length === 0" class="text-center py-8 text-gray-500">
          <div class="text-4xl mb-2">🧮</div>
          <p>No calculation rules defined</p>
          <p class="text-sm">Add rules to define how this variable is calculated</p>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="(condition, index) in conditions"
            :key="condition.id"
            class="bg-gray-50 border border-gray-200 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900">
                Rule {{ index + 1 }}
              </h4>
              <BaseButton
                variant="danger"
                size="sm"
                @click="removeCondition(index)"
              >
                ×
              </BaseButton>
            </div>

            <!-- IF condition -->
            <div class="grid grid-cols-12 gap-2 items-center mb-3">
              <div class="col-span-1">
                <span class="text-sm font-medium text-primary-600">IF</span>
              </div>
              
              <div class="col-span-4">
                <BaseSelect
                  :model-value="condition.if.variable"
                  :options="availableVariables"
                  placeholder="Select variable"
                  size="sm"
                  @update:model-value="updateCondition(index, 'if.variable', $event)"
                />
              </div>
              
              <div class="col-span-3">
                <BaseSelect
                  :model-value="condition.if.operator"
                  :options="operatorOptions"
                  placeholder="Operator"
                  size="sm"
                  @update:model-value="updateCondition(index, 'if.operator', $event)"
                />
              </div>
              
              <div class="col-span-4">
                <BaseInput
                  v-if="!['is_empty', 'is_not_empty'].includes(condition.if.operator)"
                  :model-value="condition.if.value"
                  placeholder="Value"
                  size="sm"
                  @update:model-value="updateCondition(index, 'if.value', $event)"
                />
              </div>
            </div>

            <!-- THEN action -->
            <div class="grid grid-cols-12 gap-2 items-center">
              <div class="col-span-1">
                <span class="text-sm font-medium text-green-600">THEN</span>
              </div>
              
              <div class="col-span-3">
                <BaseSelect
                  :model-value="condition.then.type"
                  :options="actionTypeOptions"
                  placeholder="Action"
                  size="sm"
                  @update:model-value="updateCondition(index, 'then.type', $event)"
                />
              </div>
              
              <div class="col-span-8">
                <BaseInput
                  v-if="condition.then.type === 'value'"
                  :model-value="condition.then.value"
                  placeholder="Enter value"
                  size="sm"
                  @update:model-value="updateCondition(index, 'then.value', $event)"
                />
                <BaseInput
                  v-else-if="condition.then.type === 'calculation'"
                  :model-value="condition.then.expression"
                  placeholder="Enter calculation (e.g., total_sales * 0.05)"
                  size="sm"
                  @update:model-value="updateCondition(index, 'then.expression', $event)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { ConditionalExpression, ComparisonOperator } from '@/types'
import BaseButton from '@/components/ui/BaseButton.vue'
import BaseInput from '@/components/ui/BaseInput.vue'
import BaseSelect from '@/components/ui/BaseSelect.vue'

export interface PredicateVariableProps {
  conditions: ConditionalExpression[]
  defaultValue: any
  dependencies: string[]
  availableVariables?: Array<{ value: string; label: string }>
}

const props = withDefaults(defineProps<PredicateVariableProps>(), {
  availableVariables: () => []
})

const emit = defineEmits<{
  'update:conditions': [conditions: ConditionalExpression[]]
  'update:defaultValue': [value: any]
  'update:dependencies': [dependencies: string[]]
}>()

const operatorOptions = [
  { value: 'equals', label: '=' },
  { value: 'not_equals', label: '!=' },
  { value: 'greater_than', label: '>' },
  { value: 'less_than', label: '<' },
  { value: 'greater_equal', label: '>=' },
  { value: 'less_equal', label: '<=' },
  { value: 'is_empty', label: 'is Empty' },
  { value: 'is_not_empty', label: 'is Not Empty' },
  { value: 'between', label: 'is between' }
]

const actionTypeOptions = [
  { value: 'value', label: 'Set Value' },
  { value: 'calculation', label: 'Calculate' }
]

function addCondition() {
  const newCondition: ConditionalExpression = {
    id: generateConditionId(),
    if: {
      variable: '',
      operator: 'equals' as ComparisonOperator,
      value: ''
    },
    then: {
      type: 'value',
      value: ''
    }
  }
  
  const updatedConditions = [...props.conditions, newCondition]
  emit('update:conditions', updatedConditions)
}

function removeCondition(index: number) {
  const updatedConditions = props.conditions.filter((_, i) => i !== index)
  emit('update:conditions', updatedConditions)
}

function updateCondition(index: number, path: string, value: any) {
  const updatedConditions = [...props.conditions]
  const condition = { ...updatedConditions[index] }
  
  // Handle nested property updates
  const pathParts = path.split('.')
  let target = condition as any
  
  for (let i = 0; i < pathParts.length - 1; i++) {
    target = target[pathParts[i]]
  }
  
  target[pathParts[pathParts.length - 1]] = value
  updatedConditions[index] = condition
  
  emit('update:conditions', updatedConditions)
}

function generateConditionId(): string {
  return `predcond_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
</script>
