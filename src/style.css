@import "tailwindcss";

/* Define custom colors for Tailwind CSS v4 */
@theme {
  --color-primary-50: #e8f0fe;
  --color-primary-100: #d2e3fc;
  --color-primary-500: #1a73e8;
  --color-primary-600: #1557b0;
  --color-primary-700: #1557b0;

  --color-gray-50: #f8f9fa;
  --color-gray-100: #f0f0f0;
  --color-gray-200: #e0e0e0;
  --color-gray-300: #dadce0;
  --color-gray-400: #ddd;
  --color-gray-500: #666;
  --color-gray-600: #333;
  --color-gray-700: #333;
}

/* Reference Tailwind CSS for @apply directives */
@reference "tailwindcss";


@layer base {
  html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .container {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .form-group {
    @apply space-y-1;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white;
  }
  
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 ;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 border-gray-300;
  }
  
  .btn-danger {
    @apply bg-red-500 text-white hover:bg-red-600 ;
  }
  
  .btn-sm {
    @apply px-2 py-1 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-fade-out {
    animation: fadeOut 0.3s ease-in-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-in-out;
  }
  
  .animate-slide-out {
    animation: slideOut 0.3s ease-in-out;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateX(100%);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from { 
    opacity: 1;
    transform: translateX(0);
  }
  to { 
    opacity: 0;
    transform: translateX(100%);
  }
}
