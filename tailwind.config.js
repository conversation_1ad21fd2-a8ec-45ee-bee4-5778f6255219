/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  // Tailwind CSS v4 uses a different configuration format
  // The colors are now defined in the CSS using @theme
  theme: {
    // Enable the ring plugin for focus states
    extend: {
      ringWidth: {
        DEFAULT: '3px',
      },
      ringColor: {
        DEFAULT: 'var(--color-primary-500)',
      },
    },
  },
  // Add the ring plugin for focus states
  plugins: [],
}
