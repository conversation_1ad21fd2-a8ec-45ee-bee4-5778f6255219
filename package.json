{"name": "commissions-studio", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "npm-run-all2": "^7.0.2", "postcss": "^8.5.1", "prettier": "^3.4.2", "sass": "^1.83.4", "tailwindcss": "^4.1.11", "typescript": "~5.7.3", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^28.0.0", "unplugin-vue-router": "^0.10.0", "vite": "^6.0.11", "vite-plugin-vue-devtools": "^7.7.0", "vite-plugin-vue-layouts": "^0.11.0", "vue-tsc": "^2.2.0"}}