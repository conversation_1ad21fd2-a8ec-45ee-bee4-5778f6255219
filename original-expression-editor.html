<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expression Editor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #4285f4;
            color: white;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 500;
        }

        .save-btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }

        .save-btn:hover {
            background: #1557b0;
        }

        .content {
            padding: 24px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: #f8f9fa;
        }

        .variables-section {
            margin-bottom: 24px;
        }

        .add-variable-btn {
            background: #e8f0fe;
            color: #1a73e8;
            border: 1px solid #dadce0;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            margin-bottom: 16px;
        }

        .add-variable-btn:hover {
            background: #d2e3fc;
        }

        .variable-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
        }

        .variable-content {
            flex: 1;
        }

        .variable-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .delete-variable-btn {
            background: #ea4335;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .delete-variable-btn:hover {
            background: #d33b2c;
        }

        .variable-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .variable-desc {
            font-size: 12px;
            color: #666;
        }

        .variable-value {
            font-size: 18px;
            color: #333;
            min-width: 40px;
            text-align: right;
        }

        .execution-body {
            margin-bottom: 24px;
        }

        .execution-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 16px;
        }

        .conditions-section {
            margin-bottom: 24px;
        }

        .condition-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .condition-row span {
            font-size: 14px;
            color: #333;
        }

        .condition-input {
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            min-width: 80px;
        }

        .condition-select {
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .add-condition-btn {
            color: #1a73e8;
            background: none;
            border: none;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
            margin-top: 12px;
        }

        .add-condition-btn:hover {
            text-decoration: underline;
        }

        .condition-group {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 12px;
            background: #fafafa;
            position: relative;
        }

        .condition-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .condition-type {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .remove-condition-btn {
            background: #ea4335;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .remove-condition-btn:hover {
            background: #d33b2c;
        }

        .condition-builder {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .condition-logic-row {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .condition-logic-row select,
        .condition-logic-row input {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
        }

        .then-else-section {
            margin-top: 8px;
            padding-left: 16px;
            border-left: 3px solid #1a73e8;
        }

        .action-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 4px 0;
            flex-wrap: wrap;
        }

        .action-row span {
            font-weight: 500;
            color: #1a73e8;
        }

        .variable-dropdown {
            min-width: 120px;
        }

        .operator-dropdown {
            min-width: 60px;
        }

        .value-input {
            min-width: 80px;
        }

        .expression-section {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .expression-text {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #333;
            line-height: 1.4;
            white-space: pre-wrap;
        }

        .footer {
            padding: 16px 24px;
            border-top: 1px solid #e0e0e0;
            text-align: right;
        }

        .footer .save-btn {
            background: #1a73e8;
            padding: 10px 24px;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h2 {
            font-size: 18px;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .modal-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .modal-form label {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .modal-form input,
        .modal-form select,
        .modal-form textarea {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .modal-form textarea {
            resize: vertical;
            min-height: 60px;
        }

        .modal-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .modal-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }

        .modal-btn.primary {
            background: #1a73e8;
            color: white;
        }

        .modal-btn.secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dadce0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Expression Editor</h1>
            <button class="save-btn">Save Expression</button>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Expression Name -->
            <div class="form-group">
                <label>Expression Name</label>
                <input type="text" value="Individual Sales Procedure" readonly>
            </div>

            <!-- Variables Section -->
            <div class="variables-section">
                <label>Variables</label>
                <button class="add-variable-btn">+ Add Variable</button>
                
                <!-- All default variable items removed - now empty for dynamic creation -->
            </div>

            <!-- Execution Body -->
            <div class="execution-body">
                <label>Execution Body</label>
                <div class="execution-desc">Main Execution Steps for the current Commission Structure</div>
            </div>

            <!-- Conditions -->
            <div class="conditions-section">
                <label>Conditions</label>

                <div id="conditionsContainer">
                    <!-- Default condition (existing) -->
                    <div class="condition-group" data-condition-id="0">
                        <div class="condition-header">
                            <span class="condition-type">IF Condition</span>
                            <button class="remove-condition-btn" onclick="removeCondition(0)">×</button>
                        </div>
                        <div class="condition-builder">
                            <div class="condition-logic-row">
                                <span>If</span>
                                <select class="condition-select variable-dropdown">
                                    <option value="">Select Variable</option>
                                    <!-- Will be populated with user variables -->
                                </select>
                                <select class="condition-select operator-dropdown">
                                    <option value="is_not_empty">is Not Empty</option>
                                    <option value="equals">=</option>
                                    <option value="greater_than">></option>
                                    <option value="less_than"><</option>
                                    <option value="greater_equal">>=</option>
                                    <option value="less_equal"><=</option>
                                    <option value="not_equals">!=</option>
                                </select>
                                <input type="text" class="condition-input value-input" placeholder="Value">
                            </div>
                            <div class="then-else-section">
                                <div class="action-row">
                                    <span>THEN</span>
                                    <select class="condition-select variable-dropdown">
                                        <option value="">Select Variable</option>
                                        <!-- User variables for assignment target -->
                                    </select>
                                    <span>=</span>
                                    <select class="condition-select variable-dropdown">
                                        <option value="">Select Variable</option>
                                        <!-- Reserved + user variables for calculation -->
                                    </select>
                                    <select class="condition-select operator-dropdown">
                                        <option value="*">×</option>
                                        <option value="+">+</option>
                                        <option value="-">-</option>
                                        <option value="/">/</option>
                                    </select>
                                    <select class="condition-select variable-dropdown">
                                        <option value="">Select Variable</option>
                                        <!-- Reserved + user variables for calculation -->
                                    </select>
                                </div>
                                <div class="action-row">
                                    <span>ELSE</span>
                                    <select class="condition-select variable-dropdown">
                                        <option value="">Select Variable</option>
                                        <!-- User variables for assignment target -->
                                    </select>
                                    <span>=</span>
                                    <input type="number" class="condition-input value-input" placeholder="0" value="0">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="add-condition-btn" onclick="addCondition()">+ Add condition</button>
            </div>

            <!-- Expression Display -->
            <div class="expression-section">
                <label style="margin-bottom: 8px; display: block;">Expression</label>
                <div class="expression-text">IF(revenue_Sales[commission = Inclusive_Rate * totalSales] IF(IF[New_Loan_Rate] commission = New_Loan_Rate * totalSales) ELSE (commission = Repeat_Loan_Rate * totalSales))</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <button class="save-btn">Save Expression</button>
        </div>
    </div>

    <script>
        let conditionCounter = 1;

        // Reserved system variables
        const RESERVED_VARIABLES = [
            { name: 'Total Sales', value: 'total_sales', description: 'Total sales amount for the period' },
            { name: 'PAR', value: 'par', description: 'Portfolio at Risk percentage' },
            { name: 'Portfolio Size', value: 'portfolio_size', description: 'Total portfolio size' },
            { name: 'Number of New Loans', value: 'number_of_new_loans', description: 'Count of new loans issued' },
            { name: 'Number of Repeat Loans', value: 'number_of_repeat_loans', description: 'Count of repeat loans issued' },
            { name: 'Loan Products', value: 'loan_products', description: 'Number of different loan products' }
        ];

        // Get all variables
        function getVariables() {
            const variables = [];
            document.querySelectorAll('.variable-item').forEach(item => {
                const name = item.querySelector('.variable-name').textContent;
                const desc = item.querySelector('.variable-desc').textContent;
                const value = item.querySelector('.variable-value').textContent;
                variables.push({ name, description: desc, value });
            });
            return variables;
        }

        // Updated function to get user-created variables only
        function getUserVariableOptionsHTML() {
            const variables = getVariables();
            return variables.map(variable => 
                `<option value="${variable.name.toLowerCase().replace(/\s+/g, '_')}">${variable.name}</option>`
            ).join('');
        }

        // Generate reserved variable options HTML
        function getReservedVariableOptionsHTML() {
            return RESERVED_VARIABLES.map(variable => 
                `<option value="${variable.value}">${variable.name}</option>`
            ).join('');
        }

        // Update variable dropdowns with appropriate options
        function updateVariableDropdowns() {
            const userVariableOptions = getUserVariableOptionsHTML();
            const reservedVariableOptions = getReservedVariableOptionsHTML();
            
            // Update condition variable dropdowns (LHS - user variables only)
            document.querySelectorAll('.condition-logic-row .variable-dropdown').forEach(dropdown => {
                const currentValue = dropdown.value;
                dropdown.innerHTML = `<option value="">Select Variable</option>${userVariableOptions}`;
                dropdown.value = currentValue;
            });
            
            // Update assignment target dropdowns (LHS - user variables only)
            document.querySelectorAll('.assignment-target').forEach(dropdown => {
                const currentValue = dropdown.value;
                dropdown.innerHTML = `<option value="">Select Target Variable</option>${userVariableOptions}`;
                dropdown.value = currentValue;
            });
            
            // Update calculation source dropdowns (RHS - reserved + user variables)
            document.querySelectorAll('.calculation-source').forEach(dropdown => {
                const currentValue = dropdown.value;
                dropdown.innerHTML = `<option value="">Select Source Variable</option>${reservedVariableOptions}${userVariableOptions}`;
                dropdown.value = currentValue;
            });
        }

        // Add condition functionality
        function addCondition() {
            const container = document.getElementById('conditionsContainer');
            const conditionId = conditionCounter++;
            
            const userVariableOptions = getUserVariableOptionsHTML();
            const reservedVariableOptions = getReservedVariableOptionsHTML();

            const conditionHTML = `
                <div class="condition-group" data-condition-id="${conditionId}">
                    <div class="condition-header">
                        <span class="condition-type">IF Condition ${conditionId + 1}</span>
                        <button class="remove-condition-btn" onclick="removeCondition(${conditionId})">×</button>
                    </div>
                    <div class="condition-builder">
                        <div class="condition-logic-row">
                            <span>If</span>
                            <select class="condition-select variable-dropdown" onchange="updateExpression()">
                                <option value="">Select Variable</option>
                                ${userVariableOptions}
                            </select>
                            <select class="condition-select operator-dropdown" onchange="updateExpression()">
                                <option value="">Select Operator</option>
                                <option value="is_not_empty">is Not Empty</option>
                                <option value="is_empty">is Empty</option>
                                <option value="equals">=</option>
                                <option value="greater_than">></option>
                                <option value="less_than"><</option>
                                <option value="greater_equal">>=</option>
                                <option value="less_equal"><=</option>
                                <option value="not_equals">!=</option>
                            </select>
                            <input type="text" class="condition-input value-input" placeholder="Value" onchange="updateExpression()">
                        </div>
                        <div class="then-else-section">
                            <div class="action-row">
                                <span>THEN</span>
                                <select class="condition-select variable-dropdown assignment-target" onchange="updateExpression()">
                                    <option value="">Select Target Variable</option>
                                    ${userVariableOptions}
                                </select>
                                <span>=</span>
                                <select class="condition-select variable-dropdown calculation-source" onchange="updateExpression()">
                                    <option value="">Select Source Variable</option>
                                    ${reservedVariableOptions}
                                    ${userVariableOptions}
                                </select>
                                <select class="condition-select operator-dropdown" onchange="updateExpression()">
                                    <option value="*">×</option>
                                    <option value="+">+</option>
                                    <option value="-">-</option>
                                    <option value="/">/</option>
                                </select>
                                <select class="condition-select variable-dropdown calculation-source" onchange="updateExpression()">
                                    <option value="">Select Source Variable</option>
                                    ${reservedVariableOptions}
                                    ${userVariableOptions}
                                </select>
                            </div>
                            <div class="action-row">
                                <span>ELSE</span>
                                <select class="condition-select variable-dropdown assignment-target" onchange="updateExpression()">
                                    <option value="">Select Target Variable</option>
                                    ${userVariableOptions}
                                </select>
                                <span>=</span>
                                <input type="text" class="condition-input value-input" placeholder="0" value="0" onchange="updateExpression()">
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', conditionHTML);
            updateExpression();
            showNotification('New condition added successfully!', 'success');
        }

        // Remove condition functionality
        function removeCondition(conditionId) {
            const conditionElement = document.querySelector(`[data-condition-id="${conditionId}"]`);
            if (conditionElement) {
                conditionElement.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    conditionElement.remove();
                    updateExpression();
                    showNotification('Condition removed', 'info');
                }, 300);
            }
        }

        // Update expression based on conditions
        function updateExpression() {
            const conditions = document.querySelectorAll('.condition-group');
            let expressionParts = [];

            conditions.forEach((condition, index) => {
                const variable = condition.querySelector('.variable-dropdown').value;
                const operator = condition.querySelector('.operator-dropdown').value;
                const value = condition.querySelector('.value-input').value;
                const thenVar = condition.querySelectorAll('.variable-dropdown')[1]?.value;
                const thenOp = condition.querySelectorAll('.operator-dropdown')[1]?.value;
                const thenVal = condition.querySelectorAll('.variable-dropdown')[2]?.value;
                const elseVar = condition.querySelectorAll('.variable-dropdown')[3]?.value;
                const elseVal = condition.querySelectorAll('.value-input')[1]?.value;

                if (variable && operator) {
                    let conditionText = `IF(${variable} ${getOperatorSymbol(operator)} ${value || 'value'})`;
                    if (thenVar && thenOp && thenVal) {
                        conditionText += ` THEN ${thenVar} = ${thenVar} ${thenOp} ${thenVal}`;
                    }
                    if (elseVar && elseVal !== undefined) {
                        conditionText += ` ELSE ${elseVar} = ${elseVal}`;
                    }
                    expressionParts.push(conditionText);
                }
            });

            const expressionText = expressionParts.length > 0 ?
                expressionParts.join(' AND ') :
                'IF(revenue_Sales[commission = Inclusive_Rate * totalSales] IF(IF[New_Loan_Rate] commission = New_Loan_Rate * totalSales) ELSE (commission = Repeat_Loan_Rate * totalSales))';

            document.querySelector('.expression-text').textContent = expressionText;
        }

        // Helper function to convert operator values to symbols
        function getOperatorSymbol(operator) {
            const operators = {
                'equals': '=',
                'greater_than': '>',
                'less_than': '<',
                'greater_equal': '>=',
                'less_equal': '<=',
                'not_equals': '!=',
                'is_not_empty': 'IS NOT EMPTY',
                'is_empty': 'IS EMPTY'
            };
            return operators[operator] || operator;
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 16px;
                border-radius: 4px;
                color: white;
                font-size: 14px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;

            if (type === 'success') {
                notification.style.background = '#34a853';
            } else if (type === 'error') {
                notification.style.background = '#ea4335';
            } else {
                notification.style.background = '#1a73e8';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Add variable functionality
        document.querySelectorAll('.add-variable-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                openVariableModal();
            });
        });

        // Modal functions
        function openVariableModal() {
            const modal = document.getElementById('variableModal');
            if (modal) {
                modal.style.display = 'block';
                const nameInput = document.getElementById('variableName');
                if (nameInput) {
                    nameInput.focus();
                }
            }
        }

        function closeVariableModal() {
            const modal = document.getElementById('variableModal');
            if (modal) {
                modal.style.display = 'none';
                const form = document.querySelector('.modal-form');
                if (form) {
                    form.reset();
                }
            }
        }

        function submitVariable(event) {
            event.preventDefault();
            
            const name = document.getElementById('variableName').value;
            const type = document.getElementById('variableType').value;
            const description = document.getElementById('variableDescription').value;
            const value = document.getElementById('variableValue').value || '0';
            
            if (name && type) {
                addVariable(name, type, description, value);
                closeVariableModal();
                showNotification('Variable added successfully!', 'success');
            }
        }

        // Updated addVariable function with proper selector
        function addVariable(name, type, description, value) {
            const variablesSection = document.querySelector('.variables-section');
            const addButton = variablesSection.querySelector('.add-variable-btn');
            
            if (!addButton) {
                console.error('Add variable button not found');
                return;
            }
            
            const variableHTML = `
                <div class="variable-item">
                    <div class="variable-content">
                        <div class="variable-name">${name}</div>
                        <div class="variable-desc">type: ${type} | description: ${description}</div>
                    </div>
                    <div class="variable-actions">
                        <div class="variable-value">${value}</div>
                        <button class="delete-variable-btn" onclick="deleteVariable(this)">×</button>
                    </div>
                </div>
            `;

            // Insert after the add button
            addButton.insertAdjacentHTML('afterend', variableHTML);
            
            updateVariableDropdowns();
        }

        // Delete variable function
        function deleteVariable(button) {
            const variableItem = button.closest('.variable-item');
            const variableName = variableItem.querySelector('.variable-name').textContent;
            
            if (confirm(`Are you sure you want to delete the variable "${variableName}"?`)) {
                variableItem.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    variableItem.remove();
                    updateVariableDropdowns();
                    showNotification('Variable deleted', 'info');
                }, 300);
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('variableModal');
            if (event.target === modal) {
                closeVariableModal();
            }
        }

        // Save functionality
        document.querySelectorAll('.save-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const expressionName = document.querySelector('input[type="text"]').value;
                const expression = document.querySelector('.expression-text').textContent;

                console.log('Saving expression:', {
                    name: expressionName,
                    expression: expression,
                    variables: getVariables(),
                    conditions: getConditions()
                });

                showNotification('Expression saved successfully!', 'success');
            });
        });

        // Initialize expression update and populate dropdowns on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add change listeners to existing elements
            document.querySelectorAll('.condition-select, .condition-input').forEach(element => {
                element.addEventListener('change', updateExpression);
            });

            // Add variable button functionality
            const addVariableBtn = document.querySelector('.add-variable-btn');
            if (addVariableBtn) {
                addVariableBtn.addEventListener('click', function() {
                    openVariableModal();
                });
            }

            // Initialize dropdowns with reserved variables
            updateVariableDropdowns();
            updateExpression();
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            @keyframes fadeOut {
                from { opacity: 1; transform: scale(1); }
                to { opacity: 0; transform: scale(0.95); }
            }
        `;
        document.head.appendChild(style);

        // Alternative approach - add event listener after DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Add variable button functionality
            const addVariableBtn = document.querySelector('.add-variable-btn');
            if (addVariableBtn) {
                addVariableBtn.addEventListener('click', function() {
                    openVariableModal();
                });
            }
        });
    </script>
</body>
</html>

<!-- Add modal HTML before closing body tag -->
<div id="variableModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Add New Variable</h2>
            <button class="close-btn" onclick="closeVariableModal()">&times;</button>
        </div>
        <form class="modal-form" onsubmit="submitVariable(event)">
            <div>
                <label for="variableName">Variable Name *</label>
                <input type="text" id="variableName" required placeholder="Enter variable name">
            </div>
            <div>
                <label for="variableType">Variable Type *</label>
                <select id="variableType" required>
                    <option value="">Select Type</option>
                    <option value="number">Number</option>
                    <option value="text">Text</option>
                    <option value="percentage">Percentage</option>
                    <option value="currency">Currency</option>
                    <option value="boolean">Boolean</option>
                </select>
            </div>
            <div>
                <label for="variableDescription">Description</label>
                <textarea id="variableDescription" placeholder="Enter variable description"></textarea>
            </div>
            <div>
                <label for="variableValue">Default Value</label>
                <input type="text" id="variableValue" placeholder="Enter default value">
            </div>
            <div class="modal-actions">
                <button type="button" class="modal-btn secondary" onclick="closeVariableModal()">Cancel</button>
                <button type="submit" class="modal-btn primary">Add Variable</button>
            </div>
        </form>
    </div>
</div>
























