{"version": "1.0", "metadata": {"createdAt": "2025-07-23T09:52:08.714Z", "updatedAt": "2025-07-23T09:54:01.970Z", "exportedAt": "2025-07-23T09:54:08.226Z", "exportFormat": "full"}, "expression": {"id": "expr_1753264328714_phqmsio01", "name": "Individual Sales Procedure", "description": "Main Execution Steps for the current Commission Structure", "variables": [{"id": "var_1753264345294_5537dlkuz", "name": "rate", "type": "percentage", "description": "rate", "defaultValue": 10, "createdAt": "2025-07-23T09:52:25.294Z", "updatedAt": "2025-07-23T09:52:25.294Z"}, {"id": "var_1753264362267_yt7e5b34m", "name": "result", "type": "number", "description": "result", "defaultValue": 0, "createdAt": "2025-07-23T09:52:42.267Z", "updatedAt": "2025-07-23T09:52:42.267Z"}], "conditions": [{"id": "cond_1753264364998_jmuehnp50", "variable": "total_sales", "operator": "greater_than", "value": "10000", "thenAction": {"type": "value", "target": "var_1753264362267_yt7e5b34m", "expression": "rate", "value": 0}, "elseAction": {"type": "value", "target": "var_1753264362267_yt7e5b34m", "value": "0"}, "createdAt": "2025-07-23T09:52:44.998Z", "updatedAt": "2025-07-23T09:53:42.161Z"}], "generatedExpression": "IF(Total Sales > 10000) THEN ? ELSE 0", "metadata": {"createdAt": "2025-07-23T09:52:08.714Z", "updatedAt": "2025-07-23T09:54:01.970Z"}, "version": "1.0"}, "schema": {"version": "1.0", "variableTypes": ["number", "text", "percentage", "currency", "boolean", "multi-select", "range", "predicate"], "operatorTypes": ["equals", "not_equals", "greater_than", "less_than", "greater_equal", "less_equal", "is_empty", "is_not_empty", "contains", "not_contains", "in", "not_in"], "actionTypes": ["assignment", "calculation", "value"]}}