/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BaseButton: typeof import('./src/components/ui/BaseButton.vue')['default']
    BaseInput: typeof import('./src/components/ui/BaseInput.vue')['default']
    BaseModal: typeof import('./src/components/ui/BaseModal.vue')['default']
    BaseSelect: typeof import('./src/components/ui/BaseSelect.vue')['default']
    ConditionBuilder: typeof import('./src/components/ConditionBuilder.vue')['default']
    ConditionSetBuilder: typeof import('./src/components/ConditionSetBuilder.vue')['default']
    ExpressionDisplay: typeof import('./src/components/ExpressionDisplay.vue')['default']
    ExpressionEditor: typeof import('./src/components/ExpressionEditor.vue')['default']
    ImportExportModal: typeof import('./src/components/ImportExportModal.vue')['default']
    MultiSelectVariable: typeof import('./src/components/variables/MultiSelectVariable.vue')['default']
    PredicateVariable: typeof import('./src/components/variables/PredicateVariable.vue')['default']
    RangeVariable: typeof import('./src/components/variables/RangeVariable.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TestScenarios: typeof import('./src/components/TestScenarios.vue')['default']
    VariableItem: typeof import('./src/components/variables/VariableItem.vue')['default']
    VariableManager: typeof import('./src/components/VariableManager.vue')['default']
    VariableModal: typeof import('./src/components/variables/VariableModal.vue')['default']
    VariableValueDisplay: typeof import('./src/components/variables/VariableValueDisplay.vue')['default']
  }
}
